-- 智能体个性化学习系统示例数据
-- 用于测试和演示系统功能

-- 插入用户基本信息
INSERT INTO users (id, username, real_name, email, phone, gender, birth_date) VALUES
('user_001', 'zhangsan', '张三', 'zhang<PERSON>@example.com', '13800138001', '男', '2002-03-15'),
('user_002', 'lisi', '李四', '<EMAIL>', '13800138002', '女', '2001-08-22'),
('user_003', 'wangwu', '王五', '<EMAIL>', '13800138003', '男', '2003-01-10');

-- 插入学生详细信息
INSERT INTO student_profiles (user_id, student_id, grade_level, major, class_name, enrollment_year, current_semester) VALUES
('user_001', '2022001001', '大二', '计算机科学与技术', '计科2022-1班', 2022, 4),
('user_002', '2021002001', '大三', '数据科学与大数据技术', '数据2021-1班', 2021, 6),
('user_003', '2023003001', '大一', '人工智能', 'AI2023-1班', 2023, 2);

-- 插入学习能力评估
INSERT INTO learning_assessments (user_id, subject, skill_level, assessment_score, assessment_date, assessment_type, strengths, weaknesses) VALUES
('user_001', 'Python编程', 'intermediate', 78.5, '2024-06-15', '综合评估', '逻辑思维清晰，代码结构良好', '算法优化能力需要提升'),
('user_001', '数据结构', 'beginner', 65.0, '2024-06-10', '期中考试', '基础概念掌握较好', '复杂数据结构理解不够深入'),
('user_002', '机器学习', 'advanced', 88.0, '2024-06-20', '项目评估', '理论基础扎实，实践能力强', '深度学习模型调优经验不足'),
('user_002', '统计学', 'intermediate', 82.5, '2024-06-18', '综合评估', '数学基础好，统计思维强', '实际应用场景理解需加强'),
('user_003', 'C++编程', 'beginner', 72.0, '2024-06-25', '期末考试', '学习态度认真，基础扎实', '面向对象编程概念需要加强');

-- 插入学习记录
INSERT INTO learning_records (user_id, course_name, course_code, completion_rate, current_progress, study_hours, last_study_date, performance_score, difficulty_level) VALUES
('user_001', 'Python程序设计', 'CS101', 85.5, '第8章：面向对象编程', 45.5, '2024-07-08', 78.5, 'intermediate'),
('user_001', '数据结构与算法', 'CS201', 60.0, '第5章：树结构', 32.0, '2024-07-07', 65.0, 'intermediate'),
('user_002', '机器学习基础', 'AI301', 92.0, '第12章：深度学习入门', 68.5, '2024-07-09', 88.0, 'advanced'),
('user_002', '数据挖掘技术', 'DS302', 78.0, '第9章：聚类分析', 52.0, '2024-07-08', 82.5, 'intermediate'),
('user_003', 'C++程序设计基础', 'CS102', 70.0, '第6章：函数与递归', 28.5, '2024-07-09', 72.0, 'beginner');

-- 插入兴趣偏好
INSERT INTO user_interests (user_id, interest_category, interest_name, interest_level) VALUES
('user_001', '编程技术', 'Web开发', 4),
('user_001', '编程技术', '游戏开发', 3),
('user_001', '运动健身', '篮球', 4),
('user_001', '音乐娱乐', '流行音乐', 3),
('user_002', '数据科学', '数据可视化', 5),
('user_002', '数据科学', '机器学习', 5),
('user_002', '阅读学习', '科技书籍', 4),
('user_002', '旅行摄影', '风景摄影', 3),
('user_003', '编程技术', '算法竞赛', 4),
('user_003', '编程技术', '开源项目', 3),
('user_003', '游戏娱乐', '策略游戏', 4);

-- 插入学习风格偏好
INSERT INTO learning_preferences (user_id, learning_style, preferred_pace, preferred_time, interaction_style, feedback_preference) VALUES
('user_001', 'visual', 'normal', '晚上8-10点', 'casual', '及时反馈'),
('user_002', 'reading', 'fast', '早上6-8点', 'formal', '详细分析'),
('user_003', 'kinesthetic', 'slow', '下午2-4点', 'encouraging', '鼓励为主');

-- 插入个性特征
INSERT INTO personality_traits (user_id, trait_name, trait_value, confidence_level, source) VALUES
('user_001', '学习动机', '目标导向，希望通过编程技能找到好工作', 0.85, '问卷调查'),
('user_001', '性格特点', '外向活泼，喜欢团队合作，但有时缺乏耐心', 0.78, '行为分析'),
('user_001', '学习习惯', '喜欢边做边学，不太喜欢纯理论学习', 0.82, '学习记录分析'),
('user_002', '学习动机', '对数据科学充满热情，希望从事AI相关研究', 0.92, '问卷调查'),
('user_002', '性格特点', '内向细致，逻辑思维强，追求完美', 0.88, '行为分析'),
('user_002', '学习习惯', '喜欢深入研究，会主动查阅相关资料', 0.90, '学习记录分析'),
('user_003', '学习动机', '对编程有浓厚兴趣，但还在探索具体方向', 0.75, '问卷调查'),
('user_003', '性格特点', '谨慎认真，学习态度端正，但自信心不足', 0.80, '行为分析'),
('user_003', '学习习惯', '喜欢循序渐进，需要充分理解后再进行下一步', 0.85, '学习记录分析');

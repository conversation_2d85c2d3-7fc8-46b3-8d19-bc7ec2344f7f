#!/usr/bin/env python3
"""
智能体个性化学习系统演示API
用于演示系统功能，使用模拟数据
"""

from fastapi import FastAPI, HTTPException, Security, Depends
from fastapi.security import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API安全配置
API_KEY = "dify-agent-secret-key-2024"

# FastAPI应用实例
app = FastAPI(
    title="智能体个性化学习系统演示API",
    description="演示版本，使用模拟数据展示系统功能",
    version="1.0.0-demo",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API密钥验证
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)

def verify_api_key(api_key: str = Security(api_key_header)):
    """验证API密钥"""
    if api_key != API_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid API key"
        )
    return api_key

# 模拟用户数据
MOCK_USER_DATA = {
    "user_001": {
        "basic_info": {
            "real_name": "张三",
            "gender": "男",
            "birth_date": "2002-03-15",
            "email": "<EMAIL>"
        },
        "student_info": {
            "grade_level": "大二",
            "major": "计算机科学与技术",
            "class_name": "计科2022-1班"
        },
        "assessments": [
            {
                "subject": "Python编程",
                "skill_level": "intermediate",
                "assessment_score": 78.5,
                "strengths": "逻辑思维清晰，代码结构良好",
                "weaknesses": "算法优化能力需要提升"
            },
            {
                "subject": "数据结构",
                "skill_level": "beginner",
                "assessment_score": 65.0,
                "strengths": "基础概念掌握较好",
                "weaknesses": "复杂数据结构理解不够深入"
            }
        ],
        "learning_records": [
            {
                "course_name": "Python程序设计",
                "completion_rate": 85.5,
                "current_progress": "第8章：面向对象编程",
                "performance_score": 78.5
            },
            {
                "course_name": "数据结构与算法",
                "completion_rate": 60.0,
                "current_progress": "第5章：树结构",
                "performance_score": 65.0
            }
        ],
        "interests": [
            {"interest_name": "Web开发", "interest_level": 4},
            {"interest_name": "游戏开发", "interest_level": 3},
            {"interest_name": "篮球", "interest_level": 4}
        ],
        "preferences": {
            "learning_style": "visual",
            "preferred_pace": "normal",
            "interaction_style": "casual"
        },
        "personality": [
            {
                "trait_name": "学习动机",
                "trait_value": "目标导向，希望通过编程技能找到好工作"
            },
            {
                "trait_name": "性格特点",
                "trait_value": "外向活泼，喜欢团队合作，但有时缺乏耐心"
            }
        ]
    },
    "user_002": {
        "basic_info": {
            "real_name": "李四",
            "gender": "女",
            "birth_date": "2001-08-22",
            "email": "<EMAIL>"
        },
        "student_info": {
            "grade_level": "大三",
            "major": "数据科学与大数据技术",
            "class_name": "数据2021-1班"
        },
        "assessments": [
            {
                "subject": "机器学习",
                "skill_level": "advanced",
                "assessment_score": 88.0,
                "strengths": "理论基础扎实，实践能力强",
                "weaknesses": "深度学习模型调优经验不足"
            }
        ],
        "learning_records": [
            {
                "course_name": "机器学习基础",
                "completion_rate": 92.0,
                "current_progress": "第12章：深度学习入门",
                "performance_score": 88.0
            }
        ],
        "interests": [
            {"interest_name": "数据可视化", "interest_level": 5},
            {"interest_name": "机器学习", "interest_level": 5}
        ],
        "preferences": {
            "learning_style": "reading",
            "preferred_pace": "fast",
            "interaction_style": "formal"
        },
        "personality": [
            {
                "trait_name": "学习动机",
                "trait_value": "对数据科学充满热情，希望从事AI相关研究"
            },
            {
                "trait_name": "性格特点",
                "trait_value": "内向细致，逻辑思维强，追求完美"
            }
        ]
    },
    "user_003": {
        "basic_info": {
            "real_name": "王五",
            "gender": "男",
            "birth_date": "2003-01-10",
            "email": "<EMAIL>"
        },
        "student_info": {
            "grade_level": "大一",
            "major": "人工智能",
            "class_name": "AI2023-1班"
        },
        "assessments": [
            {
                "subject": "C++编程",
                "skill_level": "beginner",
                "assessment_score": 72.0,
                "strengths": "学习态度认真，基础扎实",
                "weaknesses": "面向对象编程概念需要加强"
            }
        ],
        "learning_records": [
            {
                "course_name": "C++程序设计基础",
                "completion_rate": 70.0,
                "current_progress": "第6章：函数与递归",
                "performance_score": 72.0
            }
        ],
        "interests": [
            {"interest_name": "算法竞赛", "interest_level": 4},
            {"interest_name": "开源项目", "interest_level": 3}
        ],
        "preferences": {
            "learning_style": "kinesthetic",
            "preferred_pace": "slow",
            "interaction_style": "encouraging"
        },
        "personality": [
            {
                "trait_name": "学习动机",
                "trait_value": "对编程有浓厚兴趣，但还在探索具体方向"
            },
            {
                "trait_name": "性格特点",
                "trait_value": "谨慎认真，学习态度端正，但自信心不足"
            }
        ]
    }
}

def generate_profile_description(user_data: dict) -> str:
    """生成用户画像描述"""
    basic_info = user_data.get('basic_info', {})
    student_info = user_data.get('student_info', {})
    assessments = user_data.get('assessments', [])
    learning_records = user_data.get('learning_records', [])
    interests = user_data.get('interests', [])
    preferences = user_data.get('preferences', {})
    personality = user_data.get('personality', [])
    
    description_parts = []
    
    # 基本信息
    if basic_info:
        name = basic_info.get('real_name', '该用户')
        gender = basic_info.get('gender', '')
        age = 22 if '2002' in basic_info.get('birth_date', '') else 21  # 简化年龄计算
        
        basic_desc = f"用户姓名：{name}"
        if gender:
            basic_desc += f"，性别：{gender}"
        basic_desc += f"，年龄：{age}岁"
        description_parts.append(basic_desc + "。")
    
    # 学生信息
    if student_info:
        student_desc = f"学生信息：{student_info.get('grade_level', '')}学生"
        if student_info.get('major'):
            student_desc += f"，专业为{student_info.get('major')}"
        if student_info.get('class_name'):
            student_desc += f"，就读于{student_info.get('class_name')}"
        description_parts.append(student_desc + "。")
    
    # 学习能力评估
    if assessments:
        assessment_desc = "学习能力评估："
        strong_subjects = []
        weak_subjects = []
        
        for assessment in assessments:
            subject = assessment.get('subject', '')
            level = assessment.get('skill_level', '')
            score = assessment.get('assessment_score', 0)
            
            if score >= 80:
                strong_subjects.append(f"{subject}({level})")
            elif score < 70:
                weak_subjects.append(f"{subject}({level})")
        
        if strong_subjects:
            assessment_desc += f"擅长{', '.join(strong_subjects)}"
        if weak_subjects:
            if strong_subjects:
                assessment_desc += f"，需要加强{', '.join(weak_subjects)}"
            else:
                assessment_desc += f"需要加强{', '.join(weak_subjects)}"
        
        description_parts.append(assessment_desc + "。")
    
    # 学习记录
    if learning_records:
        current_courses = []
        for record in learning_records:
            course_name = record.get('course_name', '')
            completion_rate = record.get('completion_rate', 0)
            
            if completion_rate > 0:
                current_courses.append(f"{course_name}(完成度{completion_rate}%)")
        
        if current_courses:
            learning_desc = f"当前学习课程：{', '.join(current_courses)}"
            description_parts.append(learning_desc + "。")
    
    # 兴趣偏好
    if interests:
        high_interests = [interest.get('interest_name', '') 
                        for interest in interests 
                        if interest.get('interest_level', 0) >= 4]
        
        if high_interests:
            interest_desc = f"兴趣爱好：对{', '.join(high_interests)}特别感兴趣"
            description_parts.append(interest_desc + "。")
    
    # 学习偏好
    if preferences:
        pref_desc = "学习偏好："
        learning_style = preferences.get('learning_style', '')
        preferred_pace = preferences.get('preferred_pace', '')
        interaction_style = preferences.get('interaction_style', '')
        
        style_map = {
            'visual': '视觉学习者',
            'auditory': '听觉学习者', 
            'kinesthetic': '动手实践者',
            'reading': '阅读学习者'
        }
        
        pace_map = {
            'slow': '喜欢慢节奏学习',
            'normal': '适应正常节奏',
            'fast': '喜欢快节奏学习'
        }
        
        interaction_map = {
            'formal': '偏好正式的交流方式',
            'casual': '喜欢轻松的交流氛围',
            'encouraging': '需要鼓励和支持'
        }
        
        pref_parts = []
        if learning_style in style_map:
            pref_parts.append(style_map[learning_style])
        if preferred_pace in pace_map:
            pref_parts.append(pace_map[preferred_pace])
        if interaction_style in interaction_map:
            pref_parts.append(interaction_map[interaction_style])
        
        if pref_parts:
            pref_desc += '，'.join(pref_parts)
            description_parts.append(pref_desc + "。")
    
    # 个性特征
    if personality:
        personality_desc = "个性特征："
        traits = []
        for trait in personality:
            trait_name = trait.get('trait_name', '')
            trait_value = trait.get('trait_value', '')
            if trait_name and trait_value:
                traits.append(f"{trait_name}方面{trait_value}")
        
        if traits:
            personality_desc += '；'.join(traits)
            description_parts.append(personality_desc + "。")
    
    # 组合所有描述
    full_description = '\n'.join(description_parts)
    
    # 添加教学建议
    guidance = generate_guidance_suggestions(user_data)
    if guidance:
        full_description += f"\n\n教学建议：{guidance}"
    
    return full_description

def generate_guidance_suggestions(user_data: dict) -> str:
    """生成教学指导建议"""
    suggestions = []
    
    # 基于学习偏好的建议
    preferences = user_data.get('preferences', {})
    if preferences:
        learning_style = preferences.get('learning_style', '')
        if learning_style == 'visual':
            suggestions.append("多使用图表、图像和视觉化工具进行教学")
        elif learning_style == 'auditory':
            suggestions.append("可以通过讲解、讨论和音频材料进行教学")
        elif learning_style == 'kinesthetic':
            suggestions.append("安排更多实践操作和动手练习")
        elif learning_style == 'reading':
            suggestions.append("提供详细的文字资料和阅读材料")
    
    # 基于学习能力的建议
    assessments = user_data.get('assessments', [])
    if assessments:
        weak_areas = [a.get('subject') for a in assessments if a.get('assessment_score', 0) < 70]
        if weak_areas:
            suggestions.append(f"重点关注{', '.join(weak_areas[:2])}等薄弱环节")
    
    # 基于个性特征的建议
    personality = user_data.get('personality', [])
    for trait in personality:
        trait_value = trait.get('trait_value', '')
        if '自信心不足' in trait_value:
            suggestions.append("多给予鼓励和正面反馈，建立学习自信心")
        elif '追求完美' in trait_value:
            suggestions.append("引导其接受渐进式学习，避免完美主义压力")
    
    return '；'.join(suggestions[:3]) if suggestions else ""

# API端点定义

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "智能体个性化学习系统演示API",
        "version": "1.0.0-demo",
        "status": "running",
        "note": "这是演示版本，使用模拟数据",
        "endpoints": {
            "get_user_profile": "/get_user_profile?user_id={user_id}",
            "health_check": "/health",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database": "mock_data",
        "version": "1.0.0-demo"
    }

@app.get("/get_user_profile")
async def get_user_profile(
    user_id: str,
    api_key: str = Depends(verify_api_key),
    include_raw_data: bool = False
):
    """获取用户画像信息（演示版本）"""
    try:
        logger.info(f"正在查询用户画像: {user_id}")
        
        # 检查用户是否存在
        if user_id not in MOCK_USER_DATA:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        user_data = MOCK_USER_DATA[user_id]
        
        # 生成用户画像描述
        profile_description = generate_profile_description(user_data)
        
        # 构建响应数据
        response_data = {
            "user_id": user_id,
            "user_profile_description": profile_description,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }
        
        # 如果需要包含原始数据
        if include_raw_data:
            response_data["raw_data"] = user_data
        
        logger.info(f"用户画像查询成功: {user_id}")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询用户画像失败: {str(e)}"
        )

@app.post("/batch_user_profiles")
async def batch_get_user_profiles(
    user_ids: list[str],
    api_key: str = Depends(verify_api_key),
    include_raw_data: bool = False
):
    """批量获取用户画像信息（演示版本）"""
    try:
        if len(user_ids) > 50:
            raise HTTPException(status_code=400, detail="批量查询用户数量不能超过50个")
        
        results = []
        for user_id in user_ids:
            try:
                if user_id in MOCK_USER_DATA:
                    user_data = MOCK_USER_DATA[user_id]
                    profile_description = generate_profile_description(user_data)
                    
                    result = {
                        "user_id": user_id,
                        "user_profile_description": profile_description,
                        "status": "success"
                    }
                    if include_raw_data:
                        result["raw_data"] = user_data
                else:
                    result = {
                        "user_id": user_id,
                        "error": "用户不存在",
                        "status": "error"
                    }
                results.append(result)
            except Exception as e:
                results.append({
                    "user_id": user_id,
                    "error": str(e),
                    "status": "error"
                })
        
        return {
            "results": results,
            "total_count": len(user_ids),
            "success_count": len([r for r in results if r["status"] == "success"]),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量查询用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量查询失败: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动智能体个性化学习系统演示API")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    uvicorn.run(
        "demo_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

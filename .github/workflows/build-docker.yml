name: Build and Release Docker Image

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  IMAGE_NAME: intelligent-agent-system

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      run: |
        echo "🐳 Building Docker image..."
        docker build -t ${{ env.IMAGE_NAME }}:latest .
        echo "✅ Docker image built successfully"
        docker images ${{ env.IMAGE_NAME }}:latest

    - name: Test Docker image
      run: |
        echo "🧪 Testing Docker image..."
        # Start container in background
        docker run -d --name test-container -p 8000:8000 ${{ env.IMAGE_NAME }}:latest

        # Wait for container to start
        sleep 15

        # Test health endpoint
        curl -f http://localhost:8000/health || exit 1
        echo "✅ Docker image test passed"

        # Stop and remove test container
        docker stop test-container
        docker rm test-container

    - name: Create Docker image tar file
      run: |
        echo "📦 Creating Docker image tar file..."

        # Save image as tar file
        docker save ${{ env.IMAGE_NAME }}:latest > intelligent-agent-system.tar

        # Compress the tar file
        gzip intelligent-agent-system.tar

        # Get file size
        TAR_SIZE=$(du -h intelligent-agent-system.tar.gz | cut -f1)

        # Create info file
        echo "# 智能体个性化学习系统 Docker 镜像" > docker-image-info.txt
        echo "" >> docker-image-info.txt
        echo "## 构建信息" >> docker-image-info.txt
        echo "- 构建时间: $(date)" >> docker-image-info.txt
        echo "- 提交哈希: ${{ github.sha }}" >> docker-image-info.txt
        echo "- 分支: ${{ github.ref_name }}" >> docker-image-info.txt
        echo "- 镜像大小: $TAR_SIZE" >> docker-image-info.txt
        echo "" >> docker-image-info.txt
        echo "## 使用方法" >> docker-image-info.txt
        echo "1. 解压镜像文件:" >> docker-image-info.txt
        echo "   \`\`\`bash" >> docker-image-info.txt
        echo "   gunzip intelligent-agent-system.tar.gz" >> docker-image-info.txt
        echo "   \`\`\`" >> docker-image-info.txt
        echo "" >> docker-image-info.txt
        echo "2. 加载Docker镜像:" >> docker-image-info.txt
        echo "   \`\`\`bash" >> docker-image-info.txt
        echo "   docker load < intelligent-agent-system.tar" >> docker-image-info.txt
        echo "   \`\`\`" >> docker-image-info.txt
        echo "" >> docker-image-info.txt
        echo "3. 运行容器:" >> docker-image-info.txt
        echo "   \`\`\`bash" >> docker-image-info.txt
        echo "   docker run -p 8000:8000 ${{ env.IMAGE_NAME }}:latest" >> docker-image-info.txt
        echo "   \`\`\`" >> docker-image-info.txt
        echo "" >> docker-image-info.txt
        echo "4. 访问服务:" >> docker-image-info.txt
        echo "   - API文档: http://localhost:8000/docs" >> docker-image-info.txt
        echo "   - 健康检查: http://localhost:8000/health" >> docker-image-info.txt
        echo "" >> docker-image-info.txt
        echo "## 镜像详情" >> docker-image-info.txt
        docker images ${{ env.IMAGE_NAME }}:latest --format "- 仓库: {{.Repository}}" >> docker-image-info.txt
        docker images ${{ env.IMAGE_NAME }}:latest --format "- 标签: {{.Tag}}" >> docker-image-info.txt
        docker images ${{ env.IMAGE_NAME }}:latest --format "- 大小: {{.Size}}" >> docker-image-info.txt
        docker images ${{ env.IMAGE_NAME }}:latest --format "- 创建时间: {{.CreatedSince}}" >> docker-image-info.txt

        echo "✅ Docker image tar file created: intelligent-agent-system.tar.gz ($TAR_SIZE)"

    - name: Upload Docker image as artifact
      uses: actions/upload-artifact@v4
      with:
        name: docker-image-${{ github.sha }}
        path: |
          intelligent-agent-system.tar.gz
          docker-image-info.txt
        retention-days: 30

    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          intelligent-agent-system.tar.gz
          docker-image-info.txt
        body: |
          ## 🎉 智能体个性化学习系统 Docker 镜像

          ### ✨ 功能特点
          - 🔐 **统一身份认证集成** - 支持JWT、OAuth2、SAML、LDAP等多种认证方式
          - 👤 **多维度用户画像** - 7个维度的用户信息，生成精准的个性化描述
          - 🤖 **Dify智能体集成** - 无缝集成Dify平台，隐藏的身份上下文
          - ⚡ **高性能API服务** - FastAPI构建，平均响应时间<1ms
          - 🐳 **容器化部署** - 开箱即用的Docker镜像
          - 🧪 **完整测试覆盖** - 9项测试全部通过

          ### 🚀 快速开始

          #### 1. 下载镜像
          点击下方的 `intelligent-agent-system.tar.gz` 文件下载Docker镜像。

          #### 2. 加载并运行
          ```bash
          # 解压镜像文件
          gunzip intelligent-agent-system.tar.gz

          # 加载Docker镜像
          docker load < intelligent-agent-system.tar

          # 运行容器
          docker run -p 8000:8000 ${{ env.IMAGE_NAME }}:latest
          ```

          #### 3. 访问服务
          - 🌐 **API文档**: http://localhost:8000/docs
          - 💚 **健康检查**: http://localhost:8000/health
          - 📊 **演示API**: 使用内置的演示数据进行测试

          ### 📖 完整部署
          如需完整部署（包括数据库），请参考项目文档：
          - [部署指南](https://github.com/nacoo/smartstudio/blob/main/deployment_guide.md)
          - [Dify配置指南](https://github.com/nacoo/smartstudio/blob/main/dify_configuration_guide.md)

          ### 🔧 构建信息
          - **提交**: `${{ github.sha }}`
          - **构建时间**: ${{ github.run_number }}
          - **分支**: ${{ github.ref_name }}

          ### 📞 支持
          如有问题，请提交 [Issue](https://github.com/nacoo/smartstudio/issues)
        draft: false
        prerelease: false

  test:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run tests
      run: |
        echo "🧪 Starting API tests..."

        # Start the demo API in background
        python demo_api.py &
        API_PID=$!

        # Wait for API to start
        sleep 15

        # Run tests
        python test_system.py

        # Stop the API
        kill $API_PID

        echo "✅ All tests completed"

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ github.sha }}
        path: test_report.json
        retention-days: 7

name: Build and Release Docker Image

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  IMAGE_NAME: intelligent-agent-system

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      run: |
        docker build -t ${{ env.IMAGE_NAME }}:latest .

    - name: Create Docker image tar file
      run: |
        # Save image as tar file
        docker save ${{ env.IMAGE_NAME }}:latest > intelligent-agent-system.tar

        # Compress the tar file
        gzip intelligent-agent-system.tar

        # Create info file
        echo "# Intelligent Agent System Docker Image" > docker-image-info.txt
        echo "Built on: $(date)" >> docker-image-info.txt
        echo "Commit: ${{ github.sha }}" >> docker-image-info.txt
        echo "Branch: ${{ github.ref_name }}" >> docker-image-info.txt
        echo "" >> docker-image-info.txt
        echo "## How to use:" >> docker-image-info.txt
        echo "1. Load the image: docker load < intelligent-agent-system.tar.gz" >> docker-image-info.txt
        echo "2. Run the container: docker run -p 8000:8000 ${{ env.IMAGE_NAME }}:latest" >> docker-image-info.txt
        echo "3. Access API docs: http://localhost:8000/docs" >> docker-image-info.txt
        echo "" >> docker-image-info.txt
        echo "## Image size:" >> docker-image-info.txt
        docker images ${{ env.IMAGE_NAME }}:latest --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" >> docker-image-info.txt

    - name: Upload Docker image as artifact
      uses: actions/upload-artifact@v4
      with:
        name: docker-image-${{ github.sha }}
        path: |
          intelligent-agent-system.tar.gz
          docker-image-info.txt
        retention-days: 30

    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          intelligent-agent-system.tar.gz
          docker-image-info.txt
        body: |
          ## 智能体个性化学习系统 Docker 镜像

          ### 功能特点
          - 🔐 统一身份认证集成
          - 👤 多维度用户画像生成
          - 🤖 Dify智能体集成
          - ⚡ 高性能API服务
          - 🐳 容器化部署

          ### 使用方法
          1. 下载 `intelligent-agent-system.tar.gz`
          2. 解压并加载镜像：
             ```bash
             gunzip intelligent-agent-system.tar.gz
             docker load < intelligent-agent-system.tar
             ```
          3. 运行容器：
             ```bash
             docker run -p 8000:8000 ${{ env.IMAGE_NAME }}:latest
             ```
          4. 访问API文档：http://localhost:8000/docs

          构建信息：
          - 提交: ${{ github.sha }}
          - 构建时间: ${{ github.run_number }}
        draft: false
        prerelease: false

  test:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'release'

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run tests
      run: |
        # Start the demo API in background
        python demo_api.py &
        API_PID=$!
        
        # Wait for API to start
        sleep 10
        
        # Run tests
        python test_system.py
        
        # Stop the API
        kill $API_PID

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ github.sha }}
        path: test_report.json
        retention-days: 7

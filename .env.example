# 智能体个性化学习系统环境配置示例
# 复制此文件为 .env 并填入实际配置值

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=intelligent_agent_db
DB_USER=root
DB_PASSWORD=your_database_password

# API安全配置
API_KEY=dify-agent-secret-key-2024

# 统一身份认证配置（根据实际SSO系统调整）
SSO_ENDPOINT=https://your-sso-system.com/api
SSO_CLIENT_ID=your_client_id
SSO_CLIENT_SECRET=your_client_secret

# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Redis配置（可选，用于缓存）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 其他配置
MAX_BATCH_SIZE=50
CACHE_TTL=3600

#!/bin/bash

# 本地Docker构建测试脚本
# 模拟GitHub Actions的构建过程

set -e

IMAGE_NAME="intelligent-agent-system"

echo "🚀 开始本地Docker构建测试"
echo "=================================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请启动Docker"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 构建Docker镜像
echo "🐳 构建Docker镜像..."
docker build -t ${IMAGE_NAME}:latest .

if [ $? -eq 0 ]; then
    echo "✅ Docker镜像构建成功"
else
    echo "❌ Docker镜像构建失败"
    exit 1
fi

# 显示镜像信息
echo "📊 镜像信息:"
docker images ${IMAGE_NAME}:latest

# 测试Docker镜像
echo "🧪 测试Docker镜像..."

# 启动容器
echo "启动测试容器..."
docker run -d --name test-container -p 8001:8000 ${IMAGE_NAME}:latest

# 等待容器启动
echo "等待容器启动..."
sleep 15

# 测试健康检查端点
echo "测试健康检查端点..."
if curl -f http://localhost:8001/health > /dev/null 2>&1; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
    docker logs test-container
    docker stop test-container
    docker rm test-container
    exit 1
fi

# 测试API端点
echo "测试API端点..."
if curl -f -H "X-API-Key: dify-agent-secret-key-2024" "http://localhost:8001/get_user_profile?user_id=user_001" > /dev/null 2>&1; then
    echo "✅ API端点测试通过"
else
    echo "❌ API端点测试失败"
    docker logs test-container
    docker stop test-container
    docker rm test-container
    exit 1
fi

# 停止并删除测试容器
echo "清理测试容器..."
docker stop test-container
docker rm test-container

echo "✅ Docker镜像测试通过"

# 创建Docker镜像tar文件
echo "📦 创建Docker镜像tar文件..."

# 保存镜像为tar文件
docker save ${IMAGE_NAME}:latest > intelligent-agent-system.tar

# 压缩tar文件
gzip intelligent-agent-system.tar

# 获取文件大小
TAR_SIZE=$(du -h intelligent-agent-system.tar.gz | cut -f1)

echo "✅ Docker镜像tar文件创建成功: intelligent-agent-system.tar.gz (${TAR_SIZE})"

# 创建信息文件
cat > docker-image-info.txt << EOF
# 智能体个性化学习系统 Docker 镜像

## 构建信息
- 构建时间: $(date)
- 镜像大小: ${TAR_SIZE}
- 本地构建: 是

## 使用方法
1. 解压镜像文件:
   \`\`\`bash
   gunzip intelligent-agent-system.tar.gz
   \`\`\`

2. 加载Docker镜像:
   \`\`\`bash
   docker load < intelligent-agent-system.tar
   \`\`\`

3. 运行容器:
   \`\`\`bash
   docker run -p 8000:8000 ${IMAGE_NAME}:latest
   \`\`\`

4. 访问服务:
   - API文档: http://localhost:8000/docs
   - 健康检查: http://localhost:8000/health

## 镜像详情
$(docker images ${IMAGE_NAME}:latest --format "- 仓库: {{.Repository}}")
$(docker images ${IMAGE_NAME}:latest --format "- 标签: {{.Tag}}")
$(docker images ${IMAGE_NAME}:latest --format "- 大小: {{.Size}}")
$(docker images ${IMAGE_NAME}:latest --format "- 创建时间: {{.CreatedSince}}")
EOF

echo "✅ 信息文件创建成功: docker-image-info.txt"

echo ""
echo "🎉 本地Docker构建测试完成！"
echo "=================================="
echo "📦 生成的文件:"
echo "   - intelligent-agent-system.tar.gz (${TAR_SIZE})"
echo "   - docker-image-info.txt"
echo ""
echo "🧪 测试结果:"
echo "   - ✅ Docker镜像构建成功"
echo "   - ✅ 容器启动正常"
echo "   - ✅ 健康检查通过"
echo "   - ✅ API端点正常"
echo "   - ✅ tar文件生成成功"
echo ""
echo "📚 使用说明:"
echo "   1. 解压: gunzip intelligent-agent-system.tar.gz"
echo "   2. 加载: docker load < intelligent-agent-system.tar"
echo "   3. 运行: docker run -p 8000:8000 ${IMAGE_NAME}:latest"
echo ""
echo "🌐 访问地址:"
echo "   - API文档: http://localhost:8000/docs"
echo "   - 健康检查: http://localhost:8000/health"

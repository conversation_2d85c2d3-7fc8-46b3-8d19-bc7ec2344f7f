# 智能体个性化学习系统 Docker 配置
# Multi-stage build for optimized image size

# Build stage
FROM python:3.11-slim as builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc \
        default-libmysqlclient-dev \
        pkg-config \
        curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖到临时目录
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PATH=/root/.local/bin:$PATH

# 安装运行时依赖
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        default-libmysqlclient-dev \
        curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app

# 设置工作目录
WORKDIR /app

# 从构建阶段复制Python包到app用户目录
COPY --from=builder --chown=app:app /root/.local /home/<USER>/.local

# 复制应用代码
COPY --chown=app:app . .

# 创建必要目录
RUN mkdir -p logs static templates public uploads && chown -R app:app logs static templates public uploads

# 设置PATH环境变量
ENV PATH=/home/<USER>/.local/bin:$PATH

# 切换到非root用户
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令 - 优先使用main.py，如果不存在则使用demo_api.py
CMD ["sh", "-c", "if [ -f main.py ]; then uvicorn main:app --host 0.0.0.0 --port 8000; else uvicorn demo_api:app --host 0.0.0.0 --port 8000; fi"]

#!/bin/bash

# 智能体个性化学习系统 - 配置界面启动脚本

echo "🚀 启动智能体系统配置界面..."

# 检查是否存在配置文件
if [ ! -f "config.html" ]; then
    echo "❌ 错误: 未找到config.html文件"
    exit 1
fi

# 获取当前目录的绝对路径
CURRENT_DIR=$(pwd)
CONFIG_FILE="file://${CURRENT_DIR}/config.html"

echo "📱 配置界面地址: ${CONFIG_FILE}"

# 尝试在不同操作系统上打开浏览器
if command -v open &> /dev/null; then
    # macOS
    echo "🍎 在macOS上打开配置界面..."
    open "${CONFIG_FILE}"
elif command -v xdg-open &> /dev/null; then
    # Linux
    echo "🐧 在Linux上打开配置界面..."
    xdg-open "${CONFIG_FILE}"
elif command -v start &> /dev/null; then
    # Windows
    echo "🪟 在Windows上打开配置界面..."
    start "${CONFIG_FILE}"
else
    echo "⚠️  无法自动打开浏览器，请手动访问以下地址："
    echo "   ${CONFIG_FILE}"
fi

echo ""
echo "📋 使用说明："
echo "1. 在配置界面中逐步配置数据库、身份认证、Dify对接等参数"
echo "2. 配置完成后点击'生成配置文件'按钮下载.env和dify_config.json文件"
echo "3. 将下载的配置文件放置到项目根目录"
echo "4. 运行 python3 main.py 启动主服务"
echo ""
echo "🔗 相关链接："
echo "   - GitHub仓库: https://github.com/nacoo/smartstudio"
echo "   - 部署指南: https://github.com/nacoo/smartstudio/blob/main/deployment_guide.md"
echo "   - Dify配置: https://github.com/nacoo/smartstudio/blob/main/dify_configuration_guide.md"

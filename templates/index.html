{% extends "base.html" %}

{% block title %}系统概览 - 智能体系统配置{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-house-door me-2"></i>系统概览</h2>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise me-1"></i>刷新状态
        </button>
    </div>
</div>

<!-- 系统状态卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="status-indicator {% if config.get('DB_HOST') %}status-success{% else %}status-danger{% endif %}"></div>
                <h5 class="card-title">数据库</h5>
                <p class="card-text">
                    {% if config.get('DB_HOST') %}
                        <small class="text-success">已配置</small><br>
                        <small class="text-muted">{{ config.get('DB_HOST') }}:{{ config.get('DB_PORT', '3306') }}</small>
                    {% else %}
                        <small class="text-danger">未配置</small>
                    {% endif %}
                </p>
                <a href="/database" class="btn btn-sm btn-outline-primary">配置</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="status-indicator {% if config.get('SSO_ENABLED') == 'true' %}status-success{% else %}status-warning{% endif %}"></div>
                <h5 class="card-title">身份认证</h5>
                <p class="card-text">
                    {% if config.get('SSO_ENABLED') == 'true' %}
                        <small class="text-success">已启用</small><br>
                        <small class="text-muted">{{ config.get('SSO_TYPE', 'OAuth2') }}</small>
                    {% else %}
                        <small class="text-warning">未启用</small>
                    {% endif %}
                </p>
                <a href="/auth" class="btn btn-sm btn-outline-primary">配置</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="status-indicator status-warning"></div>
                <h5 class="card-title">Dify对接</h5>
                <p class="card-text">
                    <small class="text-warning">待配置</small><br>
                    <small class="text-muted">API集成</small>
                </p>
                <a href="/dify" class="btn btn-sm btn-outline-primary">配置</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="status-indicator status-success"></div>
                <h5 class="card-title">文件管理</h5>
                <p class="card-text">
                    <small class="text-success">正常</small><br>
                    <small class="text-muted">Public目录</small>
                </p>
                <a href="/files" class="btn btn-sm btn-outline-primary">管理</a>
            </div>
        </div>
    </div>
</div>

<!-- 快速配置向导 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>配置向导</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>🚀 快速开始</h6>
                <ol class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        1. 配置数据库连接
                        <a href="/database" class="btn btn-sm btn-primary">去配置</a>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        2. 设置身份认证
                        <a href="/auth" class="btn btn-sm btn-primary">去配置</a>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        3. 对接Dify平台
                        <a href="/dify" class="btn btn-sm btn-primary">去配置</a>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        4. 上传前端文件
                        <a href="/files" class="btn btn-sm btn-primary">去管理</a>
                    </li>
                </ol>
            </div>
            <div class="col-md-6">
                <h6>📚 帮助文档</h6>
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="bi bi-book me-2"></i>部署指南
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="bi bi-gear me-2"></i>配置说明
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="bi bi-question-circle me-2"></i>常见问题
                    </a>
                    <a href="https://github.com/nacoo/smartstudio" class="list-group-item list-group-item-action">
                        <i class="bi bi-github me-2"></i>GitHub仓库
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>系统信息</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><strong>系统版本</strong></td>
                        <td>v1.0.0</td>
                    </tr>
                    <tr>
                        <td><strong>API密钥</strong></td>
                        <td>
                            {% if config.get('API_KEY') %}
                                <code>{{ config.get('API_KEY')[:8] }}...</code>
                            {% else %}
                                <span class="text-danger">未设置</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>JWT密钥</strong></td>
                        <td>
                            {% if config.get('JWT_SECRET_KEY') %}
                                <code>{{ config.get('JWT_SECRET_KEY')[:8] }}...</code>
                            {% else %}
                                <span class="text-danger">未设置</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr>
                        <td><strong>配置文件</strong></td>
                        <td><code>.env</code></td>
                    </tr>
                    <tr>
                        <td><strong>Public目录</strong></td>
                        <td><code>./public/</code></td>
                    </tr>
                    <tr>
                        <td><strong>API文档</strong></td>
                        <td><a href="http://localhost:8000/docs" target="_blank">查看文档</a></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends "base.html" %}

{% block title %}数据库配置 - 智能体系统配置{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-database me-2"></i>数据库配置</h2>
    <button type="button" class="btn btn-outline-success" id="test-db-btn" onclick="testDatabase()">
        <i class="bi bi-check-circle me-1"></i>测试连接
    </button>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-gear me-2"></i>MySQL数据库配置</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="db_host" class="form-label">数据库主机</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" 
                                   value="{{ config.get('DB_HOST', 'localhost') }}" required>
                            <div class="form-text">数据库服务器地址，如：localhost 或 *************</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="db_port" class="form-label">端口</label>
                            <input type="number" class="form-control" id="db_port" name="db_port" 
                                   value="{{ config.get('DB_PORT', '3306') }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="db_user" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" 
                                   value="{{ config.get('DB_USER', 'root') }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="db_password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="db_password" name="db_password" 
                                   value="{{ config.get('DB_PASSWORD', '') }}">
                            <div class="form-text">数据库用户密码</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_name" class="form-label">数据库名称</label>
                        <input type="text" class="form-control" id="db_name" name="db_name" 
                               value="{{ config.get('DB_NAME', 'intelligent_agent_db') }}" required>
                        <div class="form-text">系统将使用的数据库名称</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check me-1"></i>保存配置
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="location.href='/'">
                            <i class="bi bi-arrow-left me-1"></i>返回概览
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>配置说明</h6>
            </div>
            <div class="card-body">
                <h6>📋 数据库要求</h6>
                <ul class="list-unstyled">
                    <li><i class="bi bi-check text-success me-1"></i>MySQL 8.0+</li>
                    <li><i class="bi bi-check text-success me-1"></i>UTF8MB4字符集</li>
                    <li><i class="bi bi-check text-success me-1"></i>InnoDB存储引擎</li>
                </ul>
                
                <h6 class="mt-3">🔧 初始化步骤</h6>
                <ol class="small">
                    <li>创建数据库</li>
                    <li>配置用户权限</li>
                    <li>测试连接</li>
                    <li>运行初始化脚本</li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <i class="bi bi-lightbulb me-1"></i>
                    <strong>提示：</strong>保存配置后系统会自动测试数据库连接
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-terminal me-2"></i>快速创建数据库</h6>
            </div>
            <div class="card-body">
                <p class="small">在MySQL中执行以下命令：</p>
                <pre class="bg-light p-2 rounded small"><code>CREATE DATABASE intelligent_agent_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

GRANT ALL PRIVILEGES ON intelligent_agent_db.* 
TO 'your_user'@'%' 
IDENTIFIED BY 'your_password';

FLUSH PRIVILEGES;</code></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends "base.html" %}

{% block title %}文件管理 - 智能体系统配置{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-folder me-2"></i>文件管理</h2>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
        <i class="bi bi-cloud-upload me-1"></i>上传文件
    </button>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-folder-open me-2"></i>Public目录文件</h5>
            </div>
            <div class="card-body">
                {% if public_files %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>大小</th>
                                <th>修改时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file in public_files %}
                            <tr>
                                <td>
                                    <i class="bi bi-file-earmark me-2"></i>
                                    <a href="/public/{{ file.name }}" target="_blank">{{ file.name }}</a>
                                </td>
                                <td>{{ "%.1f KB"|format(file.size / 1024) }}</td>
                                <td>{{ file.modified|int|timestamp_to_date }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/public/{{ file.name }}" target="_blank" class="btn btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteFile('{{ file.name }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-folder2-open display-1 text-muted"></i>
                    <h5 class="text-muted mt-3">暂无文件</h5>
                    <p class="text-muted">点击上传按钮添加文件到Public目录</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>使用说明</h6>
            </div>
            <div class="card-body">
                <h6>📁 Public目录</h6>
                <p class="small">Public目录用于存放Dify前端需要的静态文件，如：</p>
                <ul class="list-unstyled small">
                    <li><i class="bi bi-check text-success me-1"></i>HTML页面文件</li>
                    <li><i class="bi bi-check text-success me-1"></i>CSS样式文件</li>
                    <li><i class="bi bi-check text-success me-1"></i>JavaScript脚本</li>
                    <li><i class="bi bi-check text-success me-1"></i>图片和媒体文件</li>
                </ul>
                
                <div class="alert alert-info mt-3">
                    <i class="bi bi-lightbulb me-1"></i>
                    <strong>提示：</strong>上传的文件可通过 <code>/public/文件名</code> 访问
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-upload me-2"></i>上传限制</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>最大文件大小</strong></td>
                        <td>10MB</td>
                    </tr>
                    <tr>
                        <td><strong>支持格式</strong></td>
                        <td>所有格式</td>
                    </tr>
                    <tr>
                        <td><strong>同时上传</strong></td>
                        <td>单个文件</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-shield-check me-2"></i>安全提示</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    <strong>注意：</strong>
                    <ul class="mb-0 mt-2 small">
                        <li>不要上传敏感信息文件</li>
                        <li>确保文件来源可信</li>
                        <li>定期清理不需要的文件</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传文件模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-cloud-upload me-2"></i>上传文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">选择文件</label>
                        <input type="file" class="form-control" id="fileInput" name="file" required>
                        <div class="form-text">支持所有格式，最大10MB</div>
                    </div>
                    
                    <div class="progress" id="uploadProgress" style="display: none;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadFile()">
                    <i class="bi bi-upload me-1"></i>上传
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function uploadFile() {
    const form = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const progress = document.getElementById('uploadProgress');
    const progressBar = progress.querySelector('.progress-bar');
    
    if (!fileInput.files[0]) {
        showAlert('warning', '请选择要上传的文件');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    
    // 显示进度条
    progress.style.display = 'block';
    progressBar.style.width = '0%';
    
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
            modal.hide();
            // 刷新页面
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '上传失败: ' + error.message);
    })
    .finally(() => {
        progress.style.display = 'none';
        progressBar.style.width = '0%';
    });
}

function deleteFile(filename) {
    if (!confirm('确定要删除文件 "' + filename + '" 吗？')) {
        return;
    }
    
    fetch('/files/' + encodeURIComponent(filename), {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '删除失败: ' + error.message);
    });
}

// 文件拖拽上传
document.addEventListener('DOMContentLoaded', function() {
    const uploadModal = document.getElementById('uploadModal');
    const fileInput = document.getElementById('fileInput');
    
    // 防止默认拖拽行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadModal.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    // 拖拽上传
    uploadModal.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
        }
    }
});
</script>
{% endblock %}

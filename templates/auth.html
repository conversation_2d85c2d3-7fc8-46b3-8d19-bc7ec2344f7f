{% extends "base.html" %}

{% block title %}身份认证配置 - 智能体系统配置{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-shield-lock me-2"></i>身份认证配置</h2>
</div>

<form method="post">
    <div class="row">
        <div class="col-md-8">
            <!-- 基础配置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-toggles me-2"></i>基础配置</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="sso_enabled" name="sso_enabled" 
                                   value="true" {% if config.get('SSO_ENABLED') == 'true' %}checked{% endif %}>
                            <label class="form-check-label" for="sso_enabled">
                                <strong>启用统一身份认证 (SSO)</strong>
                            </label>
                        </div>
                        <div class="form-text">启用后用户将通过统一身份认证系统登录</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sso_type" class="form-label">认证类型</label>
                        <select class="form-select" id="sso_type" name="sso_type">
                            <option value="oauth2" {% if config.get('SSO_TYPE') == 'oauth2' %}selected{% endif %}>OAuth2</option>
                            <option value="saml" {% if config.get('SSO_TYPE') == 'saml' %}selected{% endif %}>SAML</option>
                            <option value="ldap" {% if config.get('SSO_TYPE') == 'ldap' %}selected{% endif %}>LDAP</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- OAuth2配置 -->
            <div class="card mb-4" id="oauth2-config">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-key me-2"></i>OAuth2配置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sso_client_id" class="form-label">Client ID</label>
                            <input type="text" class="form-control" id="sso_client_id" name="sso_client_id" 
                                   value="{{ config.get('SSO_CLIENT_ID', '') }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="sso_client_secret" class="form-label">Client Secret</label>
                            <input type="password" class="form-control" id="sso_client_secret" name="sso_client_secret" 
                                   value="{{ config.get('SSO_CLIENT_SECRET', '') }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sso_discovery_url" class="form-label">Discovery URL</label>
                        <input type="url" class="form-control" id="sso_discovery_url" name="sso_discovery_url" 
                               value="{{ config.get('SSO_DISCOVERY_URL', '') }}"
                               placeholder="https://your-provider.com/.well-known/openid_configuration">
                        <div class="form-text">OpenID Connect Discovery端点URL</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sso_redirect_uri" class="form-label">Redirect URI</label>
                        <input type="url" class="form-control" id="sso_redirect_uri" name="sso_redirect_uri" 
                               value="{{ config.get('SSO_REDIRECT_URI', '') }}"
                               placeholder="http://localhost:8000/auth/callback">
                        <div class="form-text">OAuth2回调地址</div>
                    </div>
                </div>
            </div>
            
            <!-- LDAP配置 -->
            <div class="card mb-4" id="ldap-config" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-diagram-3 me-2"></i>LDAP配置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="ldap_server" class="form-label">LDAP服务器</label>
                            <input type="text" class="form-control" id="ldap_server" name="ldap_server" 
                                   value="{{ config.get('LDAP_SERVER', '') }}"
                                   placeholder="ldap://your-ldap-server.com">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="ldap_port" class="form-label">端口</label>
                            <input type="number" class="form-control" id="ldap_port" name="ldap_port" 
                                   value="{{ config.get('LDAP_PORT', '389') }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="ldap_base_dn" class="form-label">Base DN</label>
                        <input type="text" class="form-control" id="ldap_base_dn" name="ldap_base_dn" 
                               value="{{ config.get('LDAP_BASE_DN', '') }}"
                               placeholder="dc=example,dc=com">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="ldap_user_dn" class="form-label">管理员DN</label>
                            <input type="text" class="form-control" id="ldap_user_dn" name="ldap_user_dn" 
                                   value="{{ config.get('LDAP_USER_DN', '') }}"
                                   placeholder="cn=admin,dc=example,dc=com">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="ldap_password" class="form-label">管理员密码</label>
                            <input type="password" class="form-control" id="ldap_password" name="ldap_password" 
                                   value="{{ config.get('LDAP_PASSWORD', '') }}">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- SAML配置 -->
            <div class="card mb-4" id="saml-config" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>SAML配置</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="saml_entity_id" class="form-label">Entity ID</label>
                        <input type="text" class="form-control" id="saml_entity_id" name="saml_entity_id" 
                               value="{{ config.get('SAML_ENTITY_ID', '') }}"
                               placeholder="https://your-app.com/saml/metadata">
                    </div>
                    
                    <div class="mb-3">
                        <label for="saml_sso_url" class="form-label">SSO URL</label>
                        <input type="url" class="form-control" id="saml_sso_url" name="saml_sso_url" 
                               value="{{ config.get('SAML_SSO_URL', '') }}"
                               placeholder="https://your-idp.com/sso/saml">
                    </div>
                    
                    <div class="mb-3">
                        <label for="saml_x509_cert" class="form-label">X.509证书</label>
                        <textarea class="form-control" id="saml_x509_cert" name="saml_x509_cert" rows="5"
                                  placeholder="-----BEGIN CERTIFICATE-----&#10;...&#10;-----END CERTIFICATE-----">{{ config.get('SAML_X509_CERT', '') }}</textarea>
                        <div class="form-text">IdP的公钥证书</div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check me-1"></i>保存配置
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="location.href='/'">
                    <i class="bi bi-arrow-left me-1"></i>返回概览
                </button>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>配置说明</h6>
                </div>
                <div class="card-body">
                    <div id="oauth2-help">
                        <h6>🔑 OAuth2配置</h6>
                        <ul class="list-unstyled small">
                            <li><i class="bi bi-check text-success me-1"></i>支持OpenID Connect</li>
                            <li><i class="bi bi-check text-success me-1"></i>自动用户信息获取</li>
                            <li><i class="bi bi-check text-success me-1"></i>安全令牌验证</li>
                        </ul>

                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-1"></i>
                            <strong>提示：</strong>需要在OAuth2提供商处注册应用并获取Client ID和Secret
                        </div>
                    </div>

                    <div id="ldap-help" style="display: none;">
                        <h6>🌐 LDAP配置</h6>
                        <ul class="list-unstyled small">
                            <li><i class="bi bi-check text-success me-1"></i>Active Directory支持</li>
                            <li><i class="bi bi-check text-success me-1"></i>用户组权限映射</li>
                            <li><i class="bi bi-check text-success me-1"></i>安全连接(LDAPS)</li>
                        </ul>

                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            <strong>注意：</strong>确保LDAP服务器可访问且管理员账户有查询权限
                        </div>
                    </div>

                    <div id="saml-help" style="display: none;">
                        <h6>🛡️ SAML配置</h6>
                        <ul class="list-unstyled small">
                            <li><i class="bi bi-check text-success me-1"></i>SAML 2.0协议</li>
                            <li><i class="bi bi-check text-success me-1"></i>加密断言支持</li>
                            <li><i class="bi bi-check text-success me-1"></i>属性映射</li>
                        </ul>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-1"></i>
                            <strong>说明：</strong>需要从IdP获取元数据和证书信息
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-list-check me-2"></i>配置检查</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">SSO启用状态</span>
                        <span class="badge {% if config.get('SSO_ENABLED') == 'true' %}bg-success{% else %}bg-secondary{% endif %}">
                            {% if config.get('SSO_ENABLED') == 'true' %}已启用{% else %}未启用{% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">认证类型</span>
                        <span class="badge bg-info">{{ config.get('SSO_TYPE', 'OAuth2') }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="small">配置完整性</span>
                        <span class="badge bg-warning">待验证</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ssoTypeSelect = document.getElementById('sso_type');
    const oauth2Config = document.getElementById('oauth2-config');
    const ldapConfig = document.getElementById('ldap-config');
    const samlConfig = document.getElementById('saml-config');
    const oauth2Help = document.getElementById('oauth2-help');
    const ldapHelp = document.getElementById('ldap-help');
    const samlHelp = document.getElementById('saml-help');

    function toggleConfigs() {
        const selectedType = ssoTypeSelect.value;

        // 隐藏所有配置
        oauth2Config.style.display = 'none';
        ldapConfig.style.display = 'none';
        samlConfig.style.display = 'none';
        oauth2Help.style.display = 'none';
        ldapHelp.style.display = 'none';
        samlHelp.style.display = 'none';

        // 显示选中的配置
        if (selectedType === 'oauth2') {
            oauth2Config.style.display = 'block';
            oauth2Help.style.display = 'block';
        } else if (selectedType === 'ldap') {
            ldapConfig.style.display = 'block';
            ldapHelp.style.display = 'block';
        } else if (selectedType === 'saml') {
            samlConfig.style.display = 'block';
            samlHelp.style.display = 'block';
        }
    }

    ssoTypeSelect.addEventListener('change', toggleConfigs);
    toggleConfigs(); // 初始化显示
});
</script>
{% endblock %}

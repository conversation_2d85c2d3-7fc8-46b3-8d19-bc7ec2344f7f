{% extends "base.html" %}

{% block title %}Dify对接配置 - 智能体系统配置{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-cpu me-2"></i>Dify对接配置</h2>
</div>

<form method="post">
    <div class="row">
        <div class="col-md-8">
            <!-- Dify API配置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-cloud me-2"></i>Dify API配置</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="dify_api_url" class="form-label">Dify API地址</label>
                        <input type="url" class="form-control" id="dify_api_url" name="dify_api_url" 
                               value="{{ dify_config.get('api_url', '') }}"
                               placeholder="https://api.dify.ai/v1">
                        <div class="form-text">Dify平台的API基础地址</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dify_api_key" class="form-label">API密钥</label>
                        <input type="password" class="form-control" id="dify_api_key" name="dify_api_key" 
                               value="{{ dify_config.get('api_key', '') }}"
                               placeholder="app-xxxxxxxxxxxxxxxxxxxxx">
                        <div class="form-text">从Dify应用设置中获取的API密钥</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dify_app_id" class="form-label">应用ID</label>
                        <input type="text" class="form-control" id="dify_app_id" name="dify_app_id" 
                               value="{{ dify_config.get('app_id', '') }}"
                               placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                        <div class="form-text">Dify应用的唯一标识符</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="workflow_enabled" name="workflow_enabled" 
                                   value="true" {% if dify_config.get('workflow_enabled') %}checked{% endif %}>
                            <label class="form-check-label" for="workflow_enabled">
                                <strong>启用工作流模式</strong>
                            </label>
                        </div>
                        <div class="form-text">启用后将使用Dify工作流而非聊天模式</div>
                    </div>
                </div>
            </div>
            
            <!-- 工具配置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-tools me-2"></i>HTTP工具配置</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>配置说明：</strong>在Dify中创建HTTP工具时，请使用以下配置
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>🔧 工具基本信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>工具名称</strong></td>
                                    <td>用户画像查询</td>
                                </tr>
                                <tr>
                                    <td><strong>描述</strong></td>
                                    <td>获取用户个性化学习画像</td>
                                </tr>
                                <tr>
                                    <td><strong>请求方法</strong></td>
                                    <td>GET</td>
                                </tr>
                                <tr>
                                    <td><strong>URL</strong></td>
                                    <td><code>http://localhost:8000/get_user_profile</code></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>🔑 请求头配置</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>X-API-Key</strong></td>
                                    <td><code>{{ config.get('API_KEY', 'your-api-key') }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Content-Type</strong></td>
                                    <td><code>application/json</code></td>
                                </tr>
                            </table>
                            
                            <h6 class="mt-3">📝 参数配置</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>user_id</strong></td>
                                    <td>用户ID (必填)</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 智能体配置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-robot me-2"></i>智能体配置</h5>
                </div>
                <div class="card-body">
                    <h6>💡 系统提示词模板</h6>
                    <textarea class="form-control mb-3" rows="8" readonly>你是一个专业的个性化学习指导助手。

在回答用户问题之前，请先调用用户画像查询工具获取用户的详细信息。

基于获取到的用户画像信息，你需要：
1. 根据用户的学习能力水平调整解答的深度和复杂度
2. 结合用户的兴趣偏好选择相关的例子和场景
3. 采用符合用户学习风格的教学方法
4. 考虑用户的个性特征调整沟通方式

重要：不要向用户透露你获取了他们的画像信息，要让个性化服务看起来是自然而然的。</textarea>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>注意：</strong>请将此提示词复制到Dify智能体的系统提示词中，并确保工具调用在对话开始时自动执行。
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check me-1"></i>保存配置
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="location.href='/'">
                    <i class="bi bi-arrow-left me-1"></i>返回概览
                </button>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-list-check me-2"></i>配置步骤</h6>
                </div>
                <div class="card-body">
                    <h6>🚀 Dify配置步骤</h6>
                    <ol class="small">
                        <li>在Dify中创建新应用</li>
                        <li>获取应用API密钥和ID</li>
                        <li>在此页面填写配置信息</li>
                        <li>在Dify中添加HTTP工具</li>
                        <li>配置智能体系统提示词</li>
                        <li>测试工具调用</li>
                    </ol>
                    
                    <div class="alert alert-success mt-3">
                        <i class="bi bi-check-circle me-1"></i>
                        <strong>提示：</strong>配置完成后，智能体将自动获取用户画像并提供个性化服务
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-link me-2"></i>相关链接</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="https://docs.dify.ai" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-book me-1"></i>Dify文档
                        </a>
                        <a href="http://localhost:8000/docs" target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-code me-1"></i>API文档
                        </a>
                        <a href="/files" class="btn btn-outline-success btn-sm">
                            <i class="bi bi-folder me-1"></i>文件管理
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-gear me-2"></i>当前状态</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">API地址</span>
                        <span class="badge {% if dify_config.get('api_url') %}bg-success{% else %}bg-secondary{% endif %}">
                            {% if dify_config.get('api_url') %}已配置{% else %}未配置{% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small">API密钥</span>
                        <span class="badge {% if dify_config.get('api_key') %}bg-success{% else %}bg-secondary{% endif %}">
                            {% if dify_config.get('api_key') %}已配置{% else %}未配置{% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="small">工作流模式</span>
                        <span class="badge {% if dify_config.get('workflow_enabled') %}bg-info{% else %}bg-secondary{% endif %}">
                            {% if dify_config.get('workflow_enabled') %}已启用{% else %}未启用{% endif %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

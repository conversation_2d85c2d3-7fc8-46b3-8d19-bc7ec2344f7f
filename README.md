# 学生学情分析智能体系统

基于Dify的智能体个性化学习系统，通过统一身份认证和多维度用户画像，实现精确的个性化学习指导。

## 🌟 系统特点

- **🔐 统一身份认证**: 支持SAML、OAuth2、LDAP等多种认证方式
- **👤 智能用户画像**: 多维度用户信息整合，包括学习能力、兴趣偏好、个性特征
- **🤖 隐藏上下文**: 用户无感知的个性化体验，身份信息完全隐藏
- **📚 学习场景优化**: 专为教育场景设计的个性化回答和指导
- **⚡ 高性能API**: FastAPI构建，支持高并发和批量查询
- **🐳 容器化部署**: Docker支持，一键部署

## 🏗️ 系统架构

```
用户 -> 统一身份认证 -> Dify智能体 -> 用户画像API -> 数据库
                                    ↓
                              个性化回答生成
```

## 📋 功能列表

### 核心功能
- [x] 用户画像查询API
- [x] 统一身份认证集成
- [x] Dify智能体工具配置
- [x] 多维度用户信息整合
- [x] 个性化提示词生成
- [x] 批量用户查询支持

### 数据维度
- [x] 用户基本信息（姓名、年龄、性别等）
- [x] 学生详细信息（年级、专业、班级等）
- [x] 学习能力评估（技能水平、优势劣势等）
- [x] 学习记录（课程进度、学习时长等）
- [x] 兴趣偏好（兴趣类别、偏好程度等）
- [x] 学习风格（视觉/听觉/动手/阅读等）
- [x] 个性特征（学习动机、性格特点等）

### 认证支持
- [x] API Key认证（用于Dify调用）
- [x] JWT令牌认证
- [x] OAuth2认证
- [x] SAML认证
- [x] LDAP认证

## 🚀 快速开始

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 或者 Python 3.11+ + MySQL 8.0+

### 一键部署
```bash
# 1. 克隆项目
git clone <your-repository-url>
cd intelligent-agent-system

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入您的配置

# 3. 启动服务
docker-compose up -d

# 4. 验证部署
curl http://localhost:8000/health
```

### 测试系统
```bash
# 运行完整测试套件
python test_system.py

# 测试特定用户画像
curl -H "X-API-Key: your-api-key" \
     "http://localhost:8000/get_user_profile?user_id=user_001"
```

## 📖 详细文档

- [部署指南](deployment_guide.md) - 完整的部署和配置说明
- [Dify配置指南](dify_configuration_guide.md) - Dify智能体配置步骤
- [API文档](http://localhost:8000/docs) - 启动服务后访问自动生成的API文档

## 🔧 配置说明

### 环境变量
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=intelligent_agent_db
DB_USER=root
DB_PASSWORD=your_password

# API安全配置
API_KEY=your_secure_api_key

# 统一身份认证配置
SSO_ENDPOINT=https://your-sso-system.com/api
SSO_CLIENT_ID=your_client_id
SSO_CLIENT_SECRET=your_client_secret
```

### 数据库结构
系统包含以下主要数据表：
- `users` - 用户基本信息
- `student_profiles` - 学生详细信息
- `learning_assessments` - 学习能力评估
- `learning_records` - 学习记录
- `user_interests` - 兴趣偏好
- `learning_preferences` - 学习风格偏好
- `personality_traits` - 个性特征

## 🔌 API接口

### 主要端点

#### 获取用户画像（API Key认证）
```http
GET /get_user_profile?user_id={user_id}
Headers: X-API-Key: your-api-key
```

#### 获取当前用户画像（SSO认证）
```http
GET /profile/me
Headers: Authorization: Bearer {token}
```

#### 批量查询用户画像
```http
POST /batch_user_profiles
Headers: X-API-Key: your-api-key
Body: ["user_001", "user_002", "user_003"]
```

#### 健康检查
```http
GET /health
```

### 响应示例
```json
{
  "user_id": "user_001",
  "user_profile_description": "用户姓名：张三，性别：男，年龄：22岁。学生信息：大二学生，专业为计算机科学与技术，就读于计科2022-1班。学习能力评估：擅长Python编程(intermediate)，需要加强数据结构(beginner)。当前学习课程：Python程序设计(完成度85.5%)，数据结构与算法(完成度60.0%)。兴趣爱好：对Web开发、篮球特别感兴趣。学习偏好：视觉学习者，适应正常节奏，喜欢轻松的交流氛围。个性特征：学习动机方面目标导向，希望通过编程技能找到好工作；性格特点方面外向活泼，喜欢团队合作，但有时缺乏耐心；学习习惯方面喜欢边做边学，不太喜欢纯理论学习。\n\n教学建议：多使用图表、图像和视觉化工具进行教学；重点关注数据结构等薄弱环节；多给予鼓励和正面反馈，建立学习自信心",
  "timestamp": "2024-07-10T10:30:00",
  "status": "success"
}
```

## 🎯 Dify集成

### 1. 创建HTTP工具
在Dify中创建名为"获取用户画像"的HTTP工具，配置API端点和参数。

### 2. 配置智能体
创建智能体应用，添加用户画像工具，配置系统提示词。

### 3. 系统提示词示例
```
# 角色定义
你是一个专业的个性化学习助手。

# 重要上下文信息
以下是当前用户的详细画像信息（对用户隐藏）：
---
{{#user_profile.user_profile_description#}}
---

# 核心任务
基于用户画像信息，提供精准的个性化学习指导。
```

## 🧪 测试

### 运行测试套件
```bash
# 完整测试
python test_system.py

# 指定服务器和API密钥
python test_system.py http://your-server:8000 your-api-key
```

### 测试覆盖
- ✅ API健康检查
- ✅ 用户画像查询
- ✅ 批量查询
- ✅ 错误处理
- ✅ API密钥验证
- ✅ 响应格式验证
- ✅ 性能测试

## 📊 监控和维护

### 日志
```bash
# 查看API服务日志
docker-compose logs -f intelligent-agent-api

# 查看数据库日志
docker-compose logs -f mysql
```

### 性能监控
```bash
# 监控API响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/health"

# 监控系统资源
htop
```

### 数据备份
```bash
# 数据库备份
mysqldump -u root -p intelligent_agent_db > backup.sql

# 定时备份（添加到crontab）
0 2 * * * /path/to/backup_script.sh
```

## 🔒 安全建议

1. **API安全**
   - 使用强密码和复杂的API密钥
   - 启用HTTPS
   - 实施速率限制
   - 定期轮换密钥

2. **数据库安全**
   - 限制数据库访问权限
   - 启用SSL连接
   - 定期更新密码
   - 监控异常访问

3. **系统安全**
   - 定期更新系统和依赖包
   - 配置防火墙
   - 启用日志审计
   - 实施访问控制

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

### 开发环境设置
```bash
# 克隆项目
git clone <repository-url>
cd intelligent-agent-system

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 运行开发服务器
uvicorn main:app --reload
```

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 📞 支持

如有问题或需要支持，请：
1. 查看[部署指南](deployment_guide.md)和[配置指南](dify_configuration_guide.md)
2. 提交[Issue](https://github.com/your-repo/issues)
3. 联系技术支持

---

**注意**: 这是一个教育用途的示例系统。在生产环境中使用前，请确保进行充分的安全评估和测试。
app:
  description: ''
  icon: grinning
  icon_background: '#E4FBCC'
  mode: workflow
  name: 学生学情分析工作流
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/ollama:0.0.3@31a23ce053d1c133da109e623afe2dc0db5f3b8981df1b2f73bce68695dc6739
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: jaguarliuu/rookie_text2data:0.3.0@59a0ef957f535edcc661369a3edc3894ce4cd8b8063b296b699ff55c5f6d262d
kind: app
version: 0.2.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: 1743493903222-source-1744094631598-target
      selected: false
      source: '1743493903222'
      sourceHandle: source
      target: '1744094631598'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: llm
      id: 1744156911306-source-1744157001431-target
      source: '1744156911306'
      sourceHandle: source
      target: '1744157001431'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1744157001431-source-1744157156625-target
      source: '1744157001431'
      sourceHandle: source
      target: '1744157156625'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: template-transform
      id: 1744157156625-source-1744157333634-target
      source: '1744157156625'
      sourceHandle: source
      target: '1744157333634'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: llm
      id: 1744157333634-source-1744157415369-target
      source: '1744157333634'
      sourceHandle: source
      target: '1744157415369'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1744157415369-source-1744157568281-target
      source: '1744157415369'
      sourceHandle: source
      target: '1744157568281'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: template-transform
      id: 1744094631598-source-1744156911306-target
      source: '1744094631598'
      sourceHandle: source
      target: '1744156911306'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 问题
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: issue
      height: 90
      id: '1743493903222'
      position:
        x: 326
        y: 296
      positionAbsolute:
        x: 326
        y: 296
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          label:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          llm_description: Database type
          max: null
          min: null
          name: db_type
          options:
          - label:
              en_US: MySQL
              ja_JP: MySQL
              pt_BR: MySQL
              zh_Hans: MySQL
            value: mysql
          - label:
              en_US: PostgreSQL
              ja_JP: PostgreSQL
              pt_BR: PostgreSQL
              zh_Hans: PostgreSQL
            value: postgresql
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: limit
            ja_JP: limit
            pt_BR: limit
            zh_Hans: SQL返回数据量限制
          label:
            en_US: limit
            ja_JP: limit
            pt_BR: limit
            zh_Hans: SQL返回数据量限制
          llm_description: limit
          max: 1000
          min: 1
          name: limit
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: json
          form: form
          human_description:
            en_US: result_format
            ja_JP: result_format
            pt_BR: result_format
            zh_Hans: 返回数据格式
          label:
            en_US: result_format
            ja_JP: result_format
            pt_BR: result_format
            zh_Hans: 返回数据格式
          llm_description: result_format
          max: null
          min: null
          name: result_format
          options:
          - label:
              en_US: JSON
              ja_JP: JSON
              pt_BR: JSON
              zh_Hans: JSON
            value: json
          - label:
              en_US: TEXT
              ja_JP: TEXT
              pt_BR: TEXT
              zh_Hans: TEXT
            value: text
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database ip/host
            ja_JP: Database ip/host
            pt_BR: Database ip/host
            zh_Hans: 数据库IP/域名
          label:
            en_US: Database ip/host
            ja_JP: Database ip/host
            pt_BR: Database ip/host
            zh_Hans: 数据库IP/域名
          llm_description: Database ip/host
          max: null
          min: null
          name: host
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database port
            ja_JP: Database port
            pt_BR: Database port
            zh_Hans: 数据库端口
          label:
            en_US: Database port
            ja_JP: Database port
            pt_BR: Database port
            zh_Hans: 数据库端口
          llm_description: Database port
          max: 65535
          min: 1
          name: port
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 数据库名称
          label:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 数据库名称
          llm_description: Database name
          max: null
          min: null
          name: db_name
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: table_names
            ja_JP: table_names
            pt_BR: table_names
            zh_Hans: 数据表名称
          label:
            en_US: table_names
            ja_JP: table_names
            pt_BR: table_names
            zh_Hans: 数据表名称
          llm_description: table_names
          max: null
          min: null
          name: table_names
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          label:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          llm_description: Username
          max: null
          min: null
          name: username
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          label:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          llm_description: Password
          max: null
          min: null
          name: password
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: secret-input
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: LLM model for text2data.
            ja_JP: LLM model for text2data.
            pt_BR: LLM model for text2data.
            zh_Hans: LLM model for text2data.
          label:
            en_US: Model
            ja_JP: Model
            pt_BR: Model
            zh_Hans: 模型
          llm_description: LLM model for text2data.
          max: null
          min: null
          name: model
          options: []
          placeholder: null
          precision: null
          required: true
          scope: llm
          template: null
          type: model-selector
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Fetching data from the database using natural language.
            ja_JP: Fetching data from the database using natural language.
            pt_BR: Fetching data from the database using natural language.
            zh_Hans: Fetching data from the database using natural language.
          label:
            en_US: Query string
            ja_JP: Query string
            pt_BR: Query string
            zh_Hans: 查询语句
          llm_description: Fetching data from the database using natural language.
          max: null
          min: null
          name: query
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: custom_prompt
            ja_JP: custom_prompt
            pt_BR: custom_prompt
            zh_Hans: 自定义提示
          label:
            en_US: custom_prompt
            ja_JP: custom_prompt
            pt_BR: custom_prompt
            zh_Hans: 自定义提示
          llm_description: custom_prompt
          max: null
          min: null
          name: custom_prompt
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: with_comment
            ja_JP: with_comment
            pt_BR: with_comment
            zh_Hans: 是否包含注释
          label:
            en_US: with_comment
            ja_JP: with_comment
            pt_BR: with_comment
            zh_Hans: 是否包含注释
          llm_description: with_comment
          max: null
          min: null
          name: with_comment
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        params:
          custom_prompt: ''
          db_name: ''
          db_type: ''
          host: ''
          limit: ''
          model: ''
          password: ''
          port: ''
          query: ''
          result_format: ''
          table_names: ''
          username: ''
          with_comment: ''
        provider_id: jaguarliuu/rookie_text2data/rookie_text2data
        provider_name: jaguarliuu/rookie_text2data/rookie_text2data
        provider_type: builtin
        selected: false
        title: 生成可执行sql语句
        tool_configurations:
          db_name: d_gov_model
          db_type: mysql
          host: **********
          limit: ''
          model:
            completion_params: {}
            mode: chat
            model: deepseek-r1:14b
            model_type: llm
            provider: langgenius/ollama/ollama
            type: model-selector
          password: Scsl@cjFx#0408
          port: 3306
          result_format: json
          username: cjfx
          with_comment: null
        tool_label: rookie_text2data
        tool_name: rookie_text2data
        tool_parameters:
          custom_prompt:
            type: mixed
            value: '请严格遵循以下要求生成JSON语句：

              1. 只输出最终有效的SQL代码，禁止包含任何自然语言、注释、思考过程或解释性文字

              2. 使用标准SQL语法，不要包含任何非SQL字符

              3.去掉<think></think>的所有内容'
          query:
            type: mixed
            value: '{{#1743493903222.issue#}}'
          table_names:
            type: mixed
            value: ''
        type: tool
      height: 324
      id: '1744094631598'
      position:
        x: 712.8336438712632
        y: 255.86381825733957
      positionAbsolute:
        x: 712.8336438712632
        y: 255.86381825733957
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: '{{ arg1 }}'
        title: 将json转为string
        type: template-transform
        variables:
        - value_selector:
          - '1744094631598'
          - json
          variable: arg1
      height: 54
      id: '1744156911306'
      position:
        x: 1062.04199973329
        y: 275.8317529498356
      positionAbsolute:
        x: 1062.04199973329
        y: 275.8317529498356
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0
            top_k: 1
            top_p: 0
          mode: chat
          name: deepseek-r1:14b
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: 7ab0ea7e-df00-47ab-b36a-2a429a7da417
          role: system
          text: "你是一个SQL语法修正器，必须且只能输出有效的SQL代码。任何解释性文字都会导致系统崩溃，请严格遵守输出格式要求。请严格遵循以下要求生成SQL语句：\n\
            \n1. 去掉上一个工作流当中的任何自然语言、注释、思考过程或解释性文字，只保留SQL代码。输出时去掉<think>和</think>的所有内容，去掉\\\
            n;\n2.只输出最终有效的SQL代码，禁止包含任何自然语言、注释、思考过程或解释性文字\n3. 使用标准SQL语法，不要包含任何非SQL字符\n\
            4. 使用以下格式包裹SQL代码：\n示例输入：\nSELECT * FORM user WHERE age = 25;\n示例输出：\n\
            ```sql\nSELECT * FROM user WHERE age = 25;\n5.特别注意：不要出现<think>标签或其他任何标记语言\n\
            示例正确输出：\nsql\nSELECT COUNT(CASE WHEN XB = '女' THEN 1 END) AS 女生数量,\n \
            \      COUNT(CASE WHEN XB = '男' THEN 1 END) AS 男生数量,\n       COUNT(*)\
            \ AS 总人数,\n       ROUND(COUNT(CASE WHEN XB = '女' THEN 1 END) * 1.0 / COUNT(*),\
            \ 2) AS 女生比例\nFROM jwxt_v_xscjb\nWHERE KCMC = '食品安全专业';\n\n错误示例（禁止出现）：\n\
            <think>这里包含思考过程...</think>\nSELECT ... -- 包含注释\n\n\n\n\n\n\nJson数据：{{#1744156911306.output#}}\n"
        selected: false
        title: 检查sql语句
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1744157001431'
      position:
        x: 1351.029191253972
        y: 275.8317529498356
      positionAbsolute:
        x: 1351.029191253972
        y: 275.8317529498356
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          label:
            en_US: Database type
            ja_JP: Database type
            pt_BR: Database type
            zh_Hans: 数据库类型
          llm_description: Database type
          max: null
          min: null
          name: db_type
          options:
          - label:
              en_US: MySQL
              ja_JP: MySQL
              pt_BR: MySQL
              zh_Hans: MySQL
            value: mysql
          - label:
              en_US: PostgreSQL
              ja_JP: PostgreSQL
              pt_BR: PostgreSQL
              zh_Hans: PostgreSQL
            value: postgresql
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: select
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database ip/host
            ja_JP: Database ip/host
            pt_BR: Database ip/host
            zh_Hans: 数据库IP/域名
          label:
            en_US: Database ip/host
            ja_JP: Database ip/host
            pt_BR: Database ip/host
            zh_Hans: 数据库IP/域名
          llm_description: Database ip/host
          max: null
          min: null
          name: host
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database port
            ja_JP: Database port
            pt_BR: Database port
            zh_Hans: 数据库端口
          label:
            en_US: Database port
            ja_JP: Database port
            pt_BR: Database port
            zh_Hans: 数据库端口
          llm_description: Database port
          max: 65535
          min: 1
          name: port
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 数据库名称
          label:
            en_US: Database name
            ja_JP: Database name
            pt_BR: Database name
            zh_Hans: 数据库名称
          llm_description: Database name
          max: null
          min: null
          name: db_name
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          label:
            en_US: Username
            ja_JP: Username
            pt_BR: Username
            zh_Hans: 用户名
          llm_description: Username
          max: null
          min: null
          name: username
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: form
          human_description:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          label:
            en_US: Password
            ja_JP: Password
            pt_BR: Password
            zh_Hans: 密码
          llm_description: Password
          max: null
          min: null
          name: password
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: secret-input
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Fetching data from the database using natural language.
            ja_JP: Fetching data from the database using natural language.
            pt_BR: Fetching data from the database using natural language.
            zh_Hans: Fetching data from the database using natural language.
          label:
            en_US: SQL string
            ja_JP: SQL string
            pt_BR: SQL string
            zh_Hans: 待执行的 SQL 语句
          llm_description: Fetching data from the database using natural language.
          max: null
          min: null
          name: sql
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: json
          form: form
          human_description:
            en_US: result_format
            ja_JP: result_format
            pt_BR: result_format
            zh_Hans: 返回数据格式
          label:
            en_US: result_format
            ja_JP: result_format
            pt_BR: result_format
            zh_Hans: 返回数据格式
          llm_description: result_format
          max: null
          min: null
          name: result_format
          options:
          - label:
              en_US: JSON
              ja_JP: JSON
              pt_BR: JSON
              zh_Hans: JSON
            value: json
          - label:
              en_US: TEXT
              ja_JP: TEXT
              pt_BR: TEXT
              zh_Hans: TEXT
            value: text
          - label:
              en_US: CSV
              ja_JP: CSV
              pt_BR: CSV
              zh_Hans: CSV
            value: csv
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        params:
          db_name: ''
          db_type: ''
          host: ''
          password: ''
          port: ''
          result_format: ''
          sql: ''
          username: ''
        provider_id: jaguarliuu/rookie_text2data/rookie_text2data
        provider_name: jaguarliuu/rookie_text2data/rookie_text2data
        provider_type: builtin
        selected: false
        title: 执行sql并返回json
        tool_configurations:
          db_name: d_gov_model
          db_type: mysql
          host: **********
          password: Scsl@cjFx#0408
          port: 3306
          result_format: json
          username: cjfx
        tool_label: rookie_excute_sql
        tool_name: rookie_excute_sql
        tool_parameters:
          sql:
            type: mixed
            value: '{{#1744157001431.text#}}'
        type: tool
      height: 246
      id: '1744157156625'
      position:
        x: 1635.5288586361391
        y: 275.8317529498356
      positionAbsolute:
        x: 1635.5288586361391
        y: 275.8317529498356
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: '{{ arg1 }}'
        title: 将json转为string
        type: template-transform
        variables:
        - value_selector:
          - '1744157156625'
          - text
          variable: arg1
      height: 54
      id: '1744157333634'
      position:
        x: 1914.1087821878914
        y: 285.7216402906109
      positionAbsolute:
        x: 1914.1087821878914
        y: 285.7216402906109
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1744157333634'
          - output
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-r1:14b
          provider: langgenius/ollama/ollama
        prompt_template:
        - id: 5a95f01f-0ae3-401e-9320-d620b1e3b791
          role: system
          text: '你是数据分析专家，分析JSON格式的sql查询结

            果，回答用户问题。

            关键规则:

            1.所有数据已符合用户问题中的条件

            2.直接使用提供的数据分析,不质疑数据是否符合条件

            3.不需要再次筛选或确认数据类别/时间范围

            4.数据为[或空或者None时直接回复"没有查询到相关数

            据",不得编造数据

            5、提供的结构是Json

            '
        - id: 40898138-2c69-4593-b027-6729eb470f5c
          role: user
          text: '数据是：{{#1744156911306.output#}}

            题是：{{#1743493903222.issue#}}

            Sql语句：{{#1744094631598.text#}}

            回答要求:

            1.列出详细数据，优先以表格方式列出数据。

            2.识别趋势、异常，并提供分析和建议。

            3.提供查询的Sql语句。

            '
        selected: false
        title: 对查询结果进行分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1744157415369'
      position:
        x: 2208.1845057663163
        y: 285.7216402906109
      positionAbsolute:
        x: 2208.1845057663163
        y: 285.7216402906109
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs: []
        selected: false
        title: 结束
        type: end
      height: 54
      id: '1744157568281'
      position:
        x: 2517.407809146093
        y: 285.7216402906109
      positionAbsolute:
        x: 2517.407809146093
        y: 285.7216402906109
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -382.3200857845409
      y: -116.99655275486899
      zoom: 0.8705505632961247

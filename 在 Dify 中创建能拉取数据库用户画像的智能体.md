# 在 Dify 中创建能拉取数据库用户画像的智能体

本文档将详细指导您如何在 Dify 中创建一个高级智能体（Agent）。该智能体能够通过调用外部 API，从您指定的数据库中拉取用户的多个字段信息，将其整合成一段“用户画像”描述，并作为隐藏的上下文（System Prompt）在每一次与用户的对话中引用，从而实现高度个性化的交互体验。

## 核心思路

由于 Dify 不能直接连接数据库，我们需要通过一个中间层 API 来实现。整个流程分为三个主要步骤：

1. **创建 API 服务**：搭建一个 Web 服务，该服务负责连接数据库、查询用户信息并将其格式化为文本。
2. **创建 Dify 工具**：将上述 API 服务封装成 Dify 中的一个 HTTP 工具。
3. **设计智能体工作流**：在智能体中编排工作流，使其在对话开始时自动调用该工具，获取用户画像，并注入到系统提示词中。

------

## 第一步：创建数据库查询 API

您需要创建一个 Web API，它接收一个用户标识（如 `user_id`），然后查询数据库，返回该用户的画像描述。这里我们以 Python 的 **FastAPI** 框架为例。

### 需求与假设

- **数据库**: PostgreSQL (您可以轻松替换为 MySQL, SQLite 等)
- **用户表**: `users`
- **查询字段**: `name`, `age`, `city`, `interests`
- **查询键**: `user_id`

### 示例代码 (`main.py`)

这是一个功能完整的 API 服务代码。

```
unfold_morepython
content_copyadd
 Show full code block import os
from fastapi import FastAPI, HTTPException, Security
from fastapi.security import APIKeyHeader
import psycopg2 # 使用适合您数据库的库，例如 mysql-connector-python for MySQL

# --- 1. 配置 ---
# 建议使用环境变量来管理敏感信息，避免硬编码
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_NAME = os.getenv("DB_NAME", "mydatabase")
DB_USER = os.getenv("DB_USER", "myuser")
DB_PASSWORD = os.getenv("DB_PASSWORD", "mypassword")

# 这是您提供给 Dify 的 API 密钥，用于保护您的 API
API_KEY = os.getenv("MY_API_KEY", "a-very-secret-key") 

# --- 2. FastAPI 应用实例 ---
app = FastAPI(
    title="User Profile API",
    description="An API to fetch user profile information for Dify Agent.",
    version="1.0.0",
)

# --- 3. API 密钥验证 ---
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)

def get_api_key(api_key: str = Security(api_key_header)):
    """验证传入的 API Key"""
    if api_key == API_KEY:
        return api_key
    else:
        raise HTTPException(
            status_code=403,
            detail="Could not validate credentials",
        )

# --- 4. 数据库连接函数 ---
def get_db_connection():
    """建立并返回一个数据库连接"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        return conn
    except psycopg2.OperationalError as e:
        print(f"Error connecting to database: {e}")
        return None

# --- 5. API 端点 (Endpoint) ---
@app.get("/get_user_profile")
def get_user_profile(user_id: str, api_key: str = Security(get_api_key)):
    """
    根据 user_id 从数据库中获取用户画像信息，并格式化为文本。
    """
    conn = get_db_connection()
    if conn is None:
        raise HTTPException(status_code=500, detail="Database connection failed")

    profile_text = "用户画像信息未找到。"
    try:
        with conn.cursor() as cur:
            # 查询您需要的多个字段
            cur.execute(
                "SELECT name, age, city, interests FROM users WHERE id = %s",
                (user_id,)
            )
            user_data = cur.fetchone()

            if user_data:
                name, age, city, interests = user_data
                # 将查询到的字段格式化成一段通顺的描述性文本
                # 这种格式化对于 LLM 理解上下文至关重要
                profile_text = (
                    f"姓名: {name}。"
                    f"年龄: {age}岁。"
                    f"居住城市: {city}。"
                    f"兴趣爱好: {interests}。"
                    f"总结: 这是一个来自{city}、名叫{name}的用户，对{interests}非常感兴趣。"
                )
    except Exception as e:
        print(f"An error occurred: {e}")
        raise HTTPException(status_code=500, detail="Error querying user data")
    finally:
        if conn:
            conn.close()

    # 返回一个包含格式化文本的 JSON 对象
    return {"user_profile_description": profile_text}
```

### 如何运行服务

1. 安装依赖:

   ```
   unfold_lessbash
   content_copyterminalpip install fastapi "uvicorn[standard]" psycopg2-binary
   ```

2. **保存代码**: 将上述代码保存为 `main.py`。

3. 启动服务: 在终端中运行以下命令。

   ```
   unfold_lessbash
   content_copyterminaluvicorn main:app --host 0.0.0.0 --port 8000 --reload
   ```

4. **验证**: 您的 API 现在运行在 `http://<your_server_ip>:8000`。您可以访问 `http://<your_server_ip>:8000/docs` 查看自动生成的 API 文档并进行测试。

------

## 第二步：在 Dify 中创建工具

接下来，我们将刚才创建的 API 注册为 Dify 的一个工具。

1. 登录 Dify，进入 **工作室 -> 工具**。
2. 点击 **创建工具**，选择 **HTTP** 类型。
3. 填写工具信息:
   - **图标和名称**: `获取用户画像`
   - **工具描述**: (这很重要，LLM 会据此判断何时使用) `用于根据用户ID从内部数据库获取用户的详细个人信息、背景和兴趣爱好，形成用户画像。`
   - API Endpoint:
     - **Method**: `GET`
     - **URL**: `http://<your_server_ip>:8000/get_user_profile` (请将 `<your_server_ip>` 替换为您部署 API 服务的公网 IP 或可访问的域名)。
   - Headers:
     - 添加一个 Header:
       - `key`: `X-API-Key`
       - `value`: `a-very-secret-key` (您在 `main.py` 中设置的 API 密钥)
   - Parameters:
     - 添加一个输入参数:
       - `key`: `user_id`
       - **参数描述**: `需要查询的用户的唯一ID`
       - **类型**: `string`
       - **必填**: 是
   - Outputs:
     - 添加一个输出参数:
       - `key`: `user_profile_description` (必须与 API 返回的 JSON 键名一致)
       - **参数描述**: `格式化后的用户画像描述文本`
       - **类型**: `string`
4. 点击 **保存并发布**。

------

## 第三步：设计和配置智能体

这是将所有部分串联起来的关键一步。

1. 进入 **工作室 -> 创建应用**，选择 **智能体（Agent）** 类型。
2. 在 **工具** 部分，点击“添加”，选择您刚刚创建的 `获取用户画像` 工具。
3. 进入 **提示词编排** 页面。

### 设计提示词 (Preamble / System Prompt)

Preamble 是智能体的核心指令，它在每次对话中都会被隐藏地提供给 LLM。

- 在 **Preamble** 编辑框中，输入以下内容：

```
unfold_moreplaintext
content_copyadd
 Show full code block # Role
你是一个高度智能的个人助理。

# Context (IMPORTANT)
以下是你当前服务用户的个人画像信息。你必须在每一次回答中都参考这些信息，以便提供最贴心、最相关、最个性化的回应。此信息对用户是隐藏的。
---
{{#user_profile.user_profile_description#}}
---

# Task
基于用户的提问，并严格结合上述用户画像信息，给出精准且充满同理心的回答。
```

- 语法解释:
  - `{{#user_profile.user_profile_description#}}` 是 Dify 的变量语法。
  - `user_profile` 是我们稍后在工作流中为工具节点设置的变量名。
  - `user_profile_description` 是该工具的输出参数名。
  - 整句话的意思是：获取名为 `user_profile` 的节点的 `user_profile_description` 输出值，并填充到这里。

### 编排（Orchestration）

在提示词编排的画布中，我们需要搭建一个自动执行的数据流。

1. **开始节点**: 此节点代表对话的开始，它包含系统变量，如 `sys.user_id`。
2. **添加工具节点**: 从左侧工具栏将 `获取用户画像` 工具拖入画布。
3. 连接变量:
   - 从 **开始** 节点的输出变量中，找到 `sys.user_id`。
   - 按住 `sys.user_id` 并拖拽一条线，连接到 `获取用户画像` 工具节点的 `user_id` 输入上。
4. 重命名节点 (关键步骤):
   - 点击 `获取用户画像` 节点。
   - 在右侧的配置面板中，将其 **节点变量名** 修改为 `user_profile`。这个名字必须与您在 Preamble 中使用的 `{{#user_profile...}}` 完全一致。
5. 检查流程:
   - 最终的工作流应该是 `开始` -> `获取用户画像` -> `大语言模型` -> `结束`。
   - `获取用户画像` 节点的输出，通过 Preamble 的变量引用，被隐式地传递给了 `大语言模型` 节点。

------

## 总结流程

当一个用户开始与这个智能体对话时，后台会自动发生以下事情：

1. **对话开始**，Dify 捕获到用户的唯一标识 `sys.user_id`。
2. 工作流将 `sys.user_id` 作为参数传递给 **获取用户画像** 工具。
3. 该工具调用您部署的 API (`/get_user_profile?user_id=xxx`)。
4. 您的 API 服务连接数据库，查询数据，格式化成一段描述文本，并通过 JSON 返回。
5. 工具节点接收到返回的画像文本。
6. **大语言模型** 节点的 Preamble 中的 `{{...}}` 变量被实际的画像文本替换。
7. LLM 在获得了这个包含用户详细背景的、隐藏的上下文后，生成对用户当前问题的回答。

至此，您已成功创建了一个能够在每次对话中都“认识”用户的智能体，可以提供真正千人千面的个性化服务。
#!/bin/bash

# 智能体个性化学习系统 - 配置界面启动脚本
# 提供图形化界面用于配置系统参数

echo "🚀 启动智能体系统配置界面..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查并安装依赖
echo "📦 检查依赖包..."
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误: 未找到requirements.txt文件"
    exit 1
fi

# 安装依赖（如果需要）
pip3 install -r requirements.txt > /dev/null 2>&1

# 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p static templates public uploads

# 检查配置界面文件
if [ ! -f "config_ui.py" ]; then
    echo "❌ 错误: 未找到config_ui.py文件"
    exit 1
fi

# 启动配置界面
echo "🌐 启动配置界面服务..."
echo "📱 访问地址: http://localhost:8080"
echo "🔧 配置完成后请重启主服务 (python3 main.py)"
echo ""
echo "按 Ctrl+C 停止服务"
echo "================================"

python3 config_ui.py

# 智能体个性化学习系统部署指南

本指南将帮助您完整部署智能体个性化学习系统，包括数据库、API服务和Dify集成。

## 系统架构概览

```
用户 -> 统一身份认证 -> Dify智能体 -> 用户画像API -> 数据库
                                    ↓
                              个性化回答生成
```

## 环境要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+) / macOS / Windows
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Python**: 3.11+ (如果不使用Docker)
- **MySQL**: 8.0+ (如果不使用Docker)

## 快速部署（推荐）

### 1. 克隆项目

```bash
git clone <your-repository-url>
cd intelligent-agent-system
```

### 2. 配置环境变量

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

关键配置项：
```env
# 数据库配置
DB_HOST=mysql
DB_NAME=intelligent_agent_db
DB_USER=root
DB_PASSWORD=your_secure_password

# API安全配置
API_KEY=your_secure_api_key

# 统一身份认证配置
SSO_ENDPOINT=https://your-sso-system.com/api
SSO_CLIENT_ID=your_client_id
SSO_CLIENT_SECRET=your_client_secret
```

### 3. 启动服务

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f intelligent-agent-api
```

### 4. 验证部署

```bash
# 检查API健康状态
curl http://localhost:8000/health

# 测试用户画像查询
curl -H "X-API-Key: your_secure_api_key" \
     "http://localhost:8000/get_user_profile?user_id=user_001"
```

## 手动部署

### 1. 数据库设置

#### 安装MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server
```

#### 创建数据库和用户
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE intelligent_agent_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'appuser'@'%' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON intelligent_agent_db.* TO 'appuser'@'%';
FLUSH PRIVILEGES;
```

#### 导入数据结构和示例数据
```bash
# 导入表结构
mysql -u appuser -p intelligent_agent_db < database_schema.sql

# 导入示例数据
mysql -u appuser -p intelligent_agent_db < sample_data.sql
```

### 2. API服务部署

#### 安装Python依赖
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 配置环境变量
```bash
# 创建.env文件
cat > .env << EOF
DB_HOST=localhost
DB_PORT=3306
DB_NAME=intelligent_agent_db
DB_USER=appuser
DB_PASSWORD=secure_password
API_KEY=your_secure_api_key
EOF
```

#### 启动API服务
```bash
# 开发环境
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 生产环境
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 3. 使用Systemd管理服务（Linux）

创建服务文件：
```bash
sudo nano /etc/systemd/system/intelligent-agent-api.service
```

服务配置：
```ini
[Unit]
Description=Intelligent Agent API Service
After=network.target mysql.service

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/project
Environment=PATH=/path/to/your/project/venv/bin
ExecStart=/path/to/your/project/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable intelligent-agent-api
sudo systemctl start intelligent-agent-api
sudo systemctl status intelligent-agent-api
```

## 统一身份认证集成

### 1. SAML集成示例

如果您使用SAML SSO系统：

```python
# 在auth_integration.py中添加SAML支持
from onelogin.saml2.auth import OneLogin_Saml2_Auth

class SAMLAuthenticator:
    def __init__(self, saml_settings):
        self.saml_settings = saml_settings
    
    def authenticate(self, request):
        auth = OneLogin_Saml2_Auth(request, self.saml_settings)
        auth.process_response()
        
        if auth.is_authenticated():
            return {
                'user_id': auth.get_nameid(),
                'username': auth.get_attribute('username')[0],
                'real_name': auth.get_attribute('displayName')[0],
                'email': auth.get_attribute('email')[0]
            }
        else:
            raise HTTPException(status_code=401, detail="SAML认证失败")
```

### 2. OAuth2集成示例

如果您使用OAuth2系统：

```python
# 配置OAuth2客户端
from authlib.integrations.fastapi_oauth2 import OAuth2AuthorizationCodeBearer

oauth2_scheme = OAuth2AuthorizationCodeBearer(
    authorizationUrl="https://your-oauth-provider.com/auth",
    tokenUrl="https://your-oauth-provider.com/token",
)
```

## Nginx反向代理配置

### 1. 安装Nginx
```bash
sudo apt install nginx  # Ubuntu/Debian
sudo yum install nginx  # CentOS/RHEL
```

### 2. 配置反向代理
```nginx
# /etc/nginx/sites-available/intelligent-agent-api
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/intelligent-agent-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL证书配置

### 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## 监控和日志

### 1. 日志配置
```python
# 在main.py中配置日志
import logging
from logging.handlers import RotatingFileHandler

# 配置日志轮转
handler = RotatingFileHandler(
    'logs/app.log', 
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
handler.setFormatter(logging.Formatter(
    '%(asctime)s %(levelname)s: %(message)s'
))
logger.addHandler(handler)
```

### 2. 性能监控
```bash
# 使用htop监控系统资源
sudo apt install htop
htop

# 监控API响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/health"
```

## 备份策略

### 1. 数据库备份
```bash
#!/bin/bash
# backup_db.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u appuser -p intelligent_agent_db > backup_${DATE}.sql
gzip backup_${DATE}.sql

# 保留最近7天的备份
find . -name "backup_*.sql.gz" -mtime +7 -delete
```

### 2. 定时备份
```bash
# 添加到crontab
crontab -e
# 每天凌晨2点备份
0 2 * * * /path/to/backup_db.sh
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   sudo systemctl status mysql
   
   # 检查端口是否开放
   netstat -tlnp | grep 3306
   
   # 测试连接
   mysql -u appuser -p -h localhost
   ```

2. **API服务启动失败**
   ```bash
   # 检查端口占用
   lsof -i :8000
   
   # 查看详细错误日志
   tail -f logs/app.log
   
   # 检查Python环境
   which python
   pip list
   ```

3. **权限问题**
   ```bash
   # 检查文件权限
   ls -la
   
   # 修改权限
   chmod +x main.py
   chown -R www-data:www-data /path/to/project
   ```

### 性能优化

1. **数据库优化**
   ```sql
   -- 添加索引
   CREATE INDEX idx_users_email ON users(email);
   CREATE INDEX idx_learning_records_date ON learning_records(last_study_date);
   
   -- 优化查询
   EXPLAIN SELECT * FROM users WHERE id = 'user_001';
   ```

2. **API服务优化**
   ```python
   # 使用连接池
   from mysql.connector import pooling
   
   config = {
       'pool_name': 'mypool',
       'pool_size': 10,
       'pool_reset_session': True,
       **connection_config
   }
   pool = pooling.MySQLConnectionPool(**config)
   ```

## 安全建议

1. **API安全**
   - 使用强密码和复杂的API密钥
   - 启用HTTPS
   - 实施速率限制
   - 定期轮换密钥

2. **数据库安全**
   - 限制数据库访问权限
   - 启用SSL连接
   - 定期更新密码
   - 监控异常访问

3. **系统安全**
   - 定期更新系统和依赖包
   - 配置防火墙
   - 启用日志审计
   - 实施访问控制

通过以上步骤，您应该能够成功部署智能体个性化学习系统。如有问题，请参考故障排除部分或联系技术支持。

version: '3.8'

services:
  # 智能体API服务
  intelligent-agent-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=intelligent_agent_db
      - DB_USER=root
      - DB_PASSWORD=rootpassword
      - API_KEY=dify-agent-secret-key-2024
    depends_on:
      mysql:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - intelligent-agent-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=intelligent_agent_db
      - MYSQL_USER=appuser
      - MYSQL_PASSWORD=apppassword
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database_schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./sample_data.sql:/docker-entrypoint-initdb.d/02-data.sql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    restart: unless-stopped
    networks:
      - intelligent-agent-network

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - intelligent-agent-network

  # phpMyAdmin（可选，用于数据库管理）
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    environment:
      - PMA_HOST=mysql
      - PMA_USER=root
      - PMA_PASSWORD=rootpassword
    ports:
      - "8080:80"
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - intelligent-agent-network

volumes:
  mysql_data:
  redis_data:

networks:
  intelligent-agent-network:
    driver: bridge

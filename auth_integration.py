"""
统一身份认证集成模块
支持多种SSO系统集成，包括SAML、OAuth2、LDAP等
"""

import os
import logging
import jwt
import requests
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

logger = logging.getLogger(__name__)

# 统一身份认证配置
SSO_ENDPOINT = os.getenv("SSO_ENDPOINT", "")
SSO_CLIENT_ID = os.getenv("SSO_CLIENT_ID", "")
SSO_CLIENT_SECRET = os.getenv("SSO_CLIENT_SECRET", "")
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-jwt-secret-key")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")

# HTTP Bearer认证
security = HTTPBearer()

class SSOAuthenticator:
    """统一身份认证器"""
    
    def __init__(self):
        self.sso_endpoint = SSO_ENDPOINT
        self.client_id = SSO_CLIENT_ID
        self.client_secret = SSO_CLIENT_SECRET
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """验证SSO令牌并获取用户信息"""
        try:
            # 方式1: JWT令牌验证（适用于JWT-based SSO）
            if self._is_jwt_token(token):
                return self._verify_jwt_token(token)
            
            # 方式2: 调用SSO API验证（适用于OAuth2等）
            else:
                return await self._verify_sso_api(token)
                
        except Exception as e:
            logger.error(f"令牌验证失败: {str(e)}")
            raise HTTPException(status_code=401, detail="身份认证失败")
    
    def _is_jwt_token(self, token: str) -> bool:
        """判断是否为JWT令牌"""
        return token.count('.') == 2
    
    def _verify_jwt_token(self, token: str) -> Dict[str, Any]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            
            # 检查令牌是否过期
            if 'exp' in payload:
                exp_timestamp = payload['exp']
                if datetime.utcnow().timestamp() > exp_timestamp:
                    raise HTTPException(status_code=401, detail="令牌已过期")
            
            # 提取用户信息
            user_info = {
                'user_id': payload.get('sub') or payload.get('user_id'),
                'username': payload.get('username'),
                'email': payload.get('email'),
                'real_name': payload.get('name') or payload.get('real_name'),
                'roles': payload.get('roles', []),
                'departments': payload.get('departments', []),
                'token_type': 'jwt'
            }
            
            return user_info
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="令牌已过期")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="无效的令牌")
    
    async def _verify_sso_api(self, token: str) -> Dict[str, Any]:
        """通过SSO API验证令牌"""
        if not self.sso_endpoint:
            raise HTTPException(status_code=500, detail="SSO服务未配置")
        
        try:
            # 调用SSO验证接口
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.sso_endpoint}/userinfo",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                user_data = response.json()
                
                # 标准化用户信息格式
                user_info = {
                    'user_id': user_data.get('id') or user_data.get('user_id') or user_data.get('sub'),
                    'username': user_data.get('username') or user_data.get('preferred_username'),
                    'email': user_data.get('email'),
                    'real_name': user_data.get('name') or user_data.get('display_name'),
                    'roles': user_data.get('roles', []),
                    'departments': user_data.get('departments', []),
                    'token_type': 'sso_api'
                }
                
                return user_info
            
            elif response.status_code == 401:
                raise HTTPException(status_code=401, detail="令牌无效或已过期")
            else:
                raise HTTPException(status_code=500, detail="SSO服务验证失败")
                
        except requests.RequestException as e:
            logger.error(f"SSO API调用失败: {str(e)}")
            raise HTTPException(status_code=500, detail="身份认证服务不可用")

class LDAPAuthenticator:
    """LDAP身份认证器（可选）"""
    
    def __init__(self):
        self.ldap_server = os.getenv("LDAP_SERVER", "")
        self.ldap_base_dn = os.getenv("LDAP_BASE_DN", "")
        self.ldap_bind_dn = os.getenv("LDAP_BIND_DN", "")
        self.ldap_bind_password = os.getenv("LDAP_BIND_PASSWORD", "")
    
    async def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        """LDAP用户认证"""
        try:
            import ldap3
            from ldap3 import Server, Connection, ALL
            
            # 连接LDAP服务器
            server = Server(self.ldap_server, get_info=ALL)
            conn = Connection(server, self.ldap_bind_dn, self.ldap_bind_password, auto_bind=True)
            
            # 搜索用户
            search_filter = f"(uid={username})"
            conn.search(self.ldap_base_dn, search_filter, attributes=['uid', 'cn', 'mail', 'departmentNumber'])
            
            if len(conn.entries) == 1:
                user_entry = conn.entries[0]
                user_dn = user_entry.entry_dn
                
                # 验证用户密码
                user_conn = Connection(server, user_dn, password)
                if user_conn.bind():
                    user_info = {
                        'user_id': str(user_entry.uid),
                        'username': str(user_entry.uid),
                        'real_name': str(user_entry.cn),
                        'email': str(user_entry.mail) if user_entry.mail else None,
                        'department': str(user_entry.departmentNumber) if user_entry.departmentNumber else None,
                        'token_type': 'ldap'
                    }
                    return user_info
                else:
                    raise HTTPException(status_code=401, detail="用户名或密码错误")
            else:
                raise HTTPException(status_code=401, detail="用户不存在")
                
        except ImportError:
            raise HTTPException(status_code=500, detail="LDAP支持未安装")
        except Exception as e:
            logger.error(f"LDAP认证失败: {str(e)}")
            raise HTTPException(status_code=500, detail="LDAP认证服务不可用")

# 创建认证器实例
sso_authenticator = SSOAuthenticator()
ldap_authenticator = LDAPAuthenticator()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """获取当前认证用户信息"""
    token = credentials.credentials
    
    try:
        # 首先尝试SSO认证
        user_info = await sso_authenticator.verify_token(token)
        
        # 验证用户ID是否存在
        if not user_info.get('user_id'):
            raise HTTPException(status_code=401, detail="无效的用户信息")
        
        logger.info(f"用户认证成功: {user_info.get('user_id')}")
        return user_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户认证过程中发生错误: {str(e)}")
        raise HTTPException(status_code=401, detail="身份认证失败")

async def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[Dict[str, Any]]:
    """获取可选的用户信息（用于不强制认证的接口）"""
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None

def require_roles(required_roles: list[str]):
    """角色权限装饰器"""
    def decorator(user: Dict[str, Any] = Depends(get_current_user)):
        user_roles = user.get('roles', [])
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=403,
                detail=f"需要以下角色之一: {', '.join(required_roles)}"
            )
        return user
    return decorator

def create_access_token(user_info: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌（用于内部系统）"""
    to_encode = user_info.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=24)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    return encoded_jwt

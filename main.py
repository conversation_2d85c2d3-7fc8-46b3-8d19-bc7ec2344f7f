"""
智能体个性化学习系统 - FastAPI服务
支持统一身份认证和多维度用户画像查询
"""

import os
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from fastapi import FastAPI, HTTPException, Security, Depends, Request, Form, UploadFile, File
from fastapi.security import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import mysql.connector
from mysql.connector import Error
import json
import shutil

# 导入身份认证模块
from auth_integration import get_current_user, get_optional_user, require_roles

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 环境变量配置
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "3306")
DB_NAME = os.getenv("DB_NAME", "intelligent_agent_db")
DB_USER = os.getenv("DB_USER", "root")
DB_PASSWORD = os.getenv("DB_PASSWORD", "password")

# API安全配置
API_KEY = os.getenv("API_KEY", "dify-agent-secret-key-2024")

# FastAPI应用实例
app = FastAPI(
    title="智能体个性化学习系统API",
    description="为Dify智能体提供用户画像查询服务，支持统一身份认证和多维度用户信息整合",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建必要的目录
os.makedirs("static", exist_ok=True)
os.makedirs("templates", exist_ok=True)
os.makedirs("public", exist_ok=True)

# 静态文件和模板配置
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/public", StaticFiles(directory="public"), name="public")
templates = Jinja2Templates(directory="templates")

# API密钥验证
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)

def verify_api_key(api_key: str = Security(api_key_header)):
    """验证API密钥"""
    if api_key != API_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid API key"
        )
    return api_key

# 数据库连接管理
class DatabaseManager:
    def __init__(self):
        self.connection_config = {
            'host': DB_HOST,
            'port': int(DB_PORT),
            'database': DB_NAME,
            'user': DB_USER,
            'password': DB_PASSWORD,
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = mysql.connector.connect(**self.connection_config)
            if connection.is_connected():
                return connection
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            raise HTTPException(status_code=500, detail="数据库连接失败")
        return None
    
    def close_connection(self, connection):
        """关闭数据库连接"""
        if connection and connection.is_connected():
            connection.close()

db_manager = DatabaseManager()

class UserProfileGenerator:
    """用户画像生成器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """获取完整的用户画像信息"""
        connection = None
        try:
            connection = self.db_manager.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # 获取用户基本信息
            basic_info = self._get_basic_info(cursor, user_id)
            if not basic_info:
                return {"error": "用户不存在"}
            
            # 获取学生信息
            student_info = self._get_student_info(cursor, user_id)
            
            # 获取学习能力评估
            assessments = self._get_learning_assessments(cursor, user_id)
            
            # 获取学习记录
            learning_records = self._get_learning_records(cursor, user_id)
            
            # 获取兴趣偏好
            interests = self._get_user_interests(cursor, user_id)
            
            # 获取学习偏好
            preferences = self._get_learning_preferences(cursor, user_id)
            
            # 获取个性特征
            personality = self._get_personality_traits(cursor, user_id)
            
            # 生成格式化的用户画像描述
            profile_description = self._generate_profile_description({
                'basic_info': basic_info,
                'student_info': student_info,
                'assessments': assessments,
                'learning_records': learning_records,
                'interests': interests,
                'preferences': preferences,
                'personality': personality
            })
            
            return {
                "user_id": user_id,
                "user_profile_description": profile_description,
                "raw_data": {
                    "basic_info": basic_info,
                    "student_info": student_info,
                    "assessments": assessments,
                    "learning_records": learning_records,
                    "interests": interests,
                    "preferences": preferences,
                    "personality": personality
                }
            }
            
        except Error as e:
            logger.error(f"查询用户画像失败: {e}")
            raise HTTPException(status_code=500, detail="查询用户画像失败")
        finally:
            if connection:
                self.db_manager.close_connection(connection)
    
    def _get_basic_info(self, cursor, user_id: str):
        """获取用户基本信息"""
        cursor.execute(
            "SELECT * FROM users WHERE id = %s",
            (user_id,)
        )
        return cursor.fetchone()
    
    def _get_student_info(self, cursor, user_id: str):
        """获取学生信息"""
        cursor.execute(
            "SELECT * FROM student_profiles WHERE user_id = %s",
            (user_id,)
        )
        return cursor.fetchone()
    
    def _get_learning_assessments(self, cursor, user_id: str):
        """获取学习能力评估"""
        cursor.execute(
            "SELECT * FROM learning_assessments WHERE user_id = %s ORDER BY assessment_date DESC",
            (user_id,)
        )
        return cursor.fetchall()
    
    def _get_learning_records(self, cursor, user_id: str):
        """获取学习记录"""
        cursor.execute(
            "SELECT * FROM learning_records WHERE user_id = %s ORDER BY last_study_date DESC",
            (user_id,)
        )
        return cursor.fetchall()
    
    def _get_user_interests(self, cursor, user_id: str):
        """获取用户兴趣"""
        cursor.execute(
            "SELECT * FROM user_interests WHERE user_id = %s ORDER BY interest_level DESC",
            (user_id,)
        )
        return cursor.fetchall()
    
    def _get_learning_preferences(self, cursor, user_id: str):
        """获取学习偏好"""
        cursor.execute(
            "SELECT * FROM learning_preferences WHERE user_id = %s",
            (user_id,)
        )
        return cursor.fetchone()
    
    def _get_personality_traits(self, cursor, user_id: str):
        """获取个性特征"""
        cursor.execute(
            "SELECT * FROM personality_traits WHERE user_id = %s ORDER BY confidence_level DESC",
            (user_id,)
        )
        return cursor.fetchall()

    def _generate_profile_description(self, data: Dict[str, Any]) -> str:
        """生成格式化的用户画像描述"""
        basic_info = data.get('basic_info', {})
        student_info = data.get('student_info', {})
        assessments = data.get('assessments', [])
        learning_records = data.get('learning_records', [])
        interests = data.get('interests', [])
        preferences = data.get('preferences', {})
        personality = data.get('personality', [])

        # 构建用户画像描述
        description_parts = []

        # 基本信息部分
        if basic_info:
            name = basic_info.get('real_name', '该用户')
            gender = basic_info.get('gender', '')
            age = self._calculate_age(basic_info.get('birth_date')) if basic_info.get('birth_date') else None

            basic_desc = f"用户姓名：{name}"
            if gender:
                basic_desc += f"，性别：{gender}"
            if age:
                basic_desc += f"，年龄：{age}岁"
            description_parts.append(basic_desc + "。")

        # 学生信息部分
        if student_info:
            student_desc = f"学生信息：{student_info.get('grade_level', '')}学生"
            if student_info.get('major'):
                student_desc += f"，专业为{student_info.get('major')}"
            if student_info.get('class_name'):
                student_desc += f"，就读于{student_info.get('class_name')}"
            description_parts.append(student_desc + "。")

        # 学习能力评估部分
        if assessments:
            assessment_desc = "学习能力评估："
            strong_subjects = []
            weak_subjects = []

            for assessment in assessments[:3]:  # 取最近3个评估
                subject = assessment.get('subject', '')
                level = assessment.get('skill_level', '')
                score = assessment.get('assessment_score', 0)

                if score >= 80:
                    strong_subjects.append(f"{subject}({level})")
                elif score < 70:
                    weak_subjects.append(f"{subject}({level})")

            if strong_subjects:
                assessment_desc += f"擅长{', '.join(strong_subjects)}"
            if weak_subjects:
                if strong_subjects:
                    assessment_desc += f"，需要加强{', '.join(weak_subjects)}"
                else:
                    assessment_desc += f"需要加强{', '.join(weak_subjects)}"

            description_parts.append(assessment_desc + "。")

        # 学习记录部分
        if learning_records:
            current_courses = []
            for record in learning_records[:3]:  # 取最近3门课程
                course_name = record.get('course_name', '')
                completion_rate = record.get('completion_rate', 0)
                progress = record.get('current_progress', '')

                if completion_rate > 0:
                    current_courses.append(f"{course_name}(完成度{completion_rate}%)")

            if current_courses:
                learning_desc = f"当前学习课程：{', '.join(current_courses)}"
                description_parts.append(learning_desc + "。")

        # 兴趣偏好部分
        if interests:
            high_interests = [interest.get('interest_name', '')
                            for interest in interests
                            if interest.get('interest_level', 0) >= 4][:5]

            if high_interests:
                interest_desc = f"兴趣爱好：对{', '.join(high_interests)}特别感兴趣"
                description_parts.append(interest_desc + "。")

        # 学习偏好部分
        if preferences:
            pref_desc = "学习偏好："
            learning_style = preferences.get('learning_style', '')
            preferred_pace = preferences.get('preferred_pace', '')
            interaction_style = preferences.get('interaction_style', '')

            style_map = {
                'visual': '视觉学习者',
                'auditory': '听觉学习者',
                'kinesthetic': '动手实践者',
                'reading': '阅读学习者'
            }

            pace_map = {
                'slow': '喜欢慢节奏学习',
                'normal': '适应正常节奏',
                'fast': '喜欢快节奏学习'
            }

            interaction_map = {
                'formal': '偏好正式的交流方式',
                'casual': '喜欢轻松的交流氛围',
                'encouraging': '需要鼓励和支持'
            }

            pref_parts = []
            if learning_style in style_map:
                pref_parts.append(style_map[learning_style])
            if preferred_pace in pace_map:
                pref_parts.append(pace_map[preferred_pace])
            if interaction_style in interaction_map:
                pref_parts.append(interaction_map[interaction_style])

            if pref_parts:
                pref_desc += '，'.join(pref_parts)
                description_parts.append(pref_desc + "。")

        # 个性特征部分
        if personality:
            personality_desc = "个性特征："
            traits = []
            for trait in personality[:3]:  # 取置信度最高的3个特征
                trait_name = trait.get('trait_name', '')
                trait_value = trait.get('trait_value', '')
                if trait_name and trait_value:
                    traits.append(f"{trait_name}方面{trait_value}")

            if traits:
                personality_desc += '；'.join(traits)
                description_parts.append(personality_desc + "。")

        # 组合所有描述
        full_description = '\n'.join(description_parts)

        # 添加指导建议
        guidance = self._generate_guidance_suggestions(data)
        if guidance:
            full_description += f"\n\n教学建议：{guidance}"

        return full_description

    def _calculate_age(self, birth_date) -> Optional[int]:
        """计算年龄"""
        if not birth_date:
            return None
        try:
            today = datetime.now().date()
            return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
        except:
            return None

    def _generate_guidance_suggestions(self, data: Dict[str, Any]) -> str:
        """生成教学指导建议"""
        suggestions = []

        # 基于学习偏好的建议
        preferences = data.get('preferences', {})
        if preferences:
            learning_style = preferences.get('learning_style', '')
            if learning_style == 'visual':
                suggestions.append("多使用图表、图像和视觉化工具进行教学")
            elif learning_style == 'auditory':
                suggestions.append("可以通过讲解、讨论和音频材料进行教学")
            elif learning_style == 'kinesthetic':
                suggestions.append("安排更多实践操作和动手练习")
            elif learning_style == 'reading':
                suggestions.append("提供详细的文字资料和阅读材料")

        # 基于学习能力的建议
        assessments = data.get('assessments', [])
        if assessments:
            weak_areas = [a.get('subject') for a in assessments if a.get('assessment_score', 0) < 70]
            if weak_areas:
                suggestions.append(f"重点关注{', '.join(weak_areas[:2])}等薄弱环节")

        # 基于个性特征的建议
        personality = data.get('personality', [])
        for trait in personality:
            trait_name = trait.get('trait_name', '')
            trait_value = trait.get('trait_value', '')
            if '自信心不足' in trait_value:
                suggestions.append("多给予鼓励和正面反馈，建立学习自信心")
            elif '追求完美' in trait_value:
                suggestions.append("引导其接受渐进式学习，避免完美主义压力")

        return '；'.join(suggestions[:3]) if suggestions else ""

# 创建用户画像生成器实例
profile_generator = UserProfileGenerator(db_manager)

# API端点定义

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径重定向到配置界面"""
    return HTMLResponse(content="""
    <html>
    <head>
        <title>智能体个性化学习系统</title>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .container { max-width: 600px; margin: 0 auto; }
            .btn { display: inline-block; padding: 12px 24px; margin: 10px;
                   background: #007bff; color: white; text-decoration: none;
                   border-radius: 5px; }
            .btn:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 智能体个性化学习系统</h1>
            <p>欢迎使用智能体个性化学习系统！</p>

            <div>
                <a href="/config" class="btn">📱 配置界面</a>
                <a href="/docs" class="btn">📚 API文档</a>
                <a href="/health" class="btn">🔍 健康检查</a>
            </div>

            <div style="margin-top: 30px;">
                <h3>🚀 快速开始</h3>
                <ol style="text-align: left; max-width: 400px; margin: 0 auto;">
                    <li>点击"配置界面"进行系统配置</li>
                    <li>配置数据库连接和身份认证</li>
                    <li>设置Dify对接参数</li>
                    <li>开始使用个性化学习服务</li>
                </ol>
            </div>

            <div style="margin-top: 30px; color: #666;">
                <p>版本: v1.1.1 | <a href="https://github.com/nacoo/smartstudio">GitHub</a></p>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 测试数据库连接
        connection = db_manager.get_connection()
        if connection:
            db_manager.close_connection(connection)
            db_status = "healthy"
        else:
            db_status = "unhealthy"
    except Exception as e:
        db_status = f"error: {str(e)}"

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database": db_status,
        "version": "1.0.0"
    }

@app.get("/get_user_profile")
async def get_user_profile(
    user_id: str,
    api_key: str = Depends(verify_api_key),
    include_raw_data: bool = False
):
    """
    获取用户画像信息（API Key认证方式，用于Dify调用）
    """
    """
    获取用户画像信息

    Args:
        user_id: 用户ID（来自统一身份认证系统）
        include_raw_data: 是否包含原始数据（默认False，仅返回格式化描述）

    Returns:
        用户画像描述和相关信息
    """
    try:
        logger.info(f"正在查询用户画像: {user_id}")

        # 获取用户画像
        profile_data = profile_generator.get_user_profile(user_id)

        if "error" in profile_data:
            raise HTTPException(status_code=404, detail=profile_data["error"])

        # 构建响应数据
        response_data = {
            "user_id": user_id,
            "user_profile_description": profile_data["user_profile_description"],
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

        # 如果需要包含原始数据
        if include_raw_data:
            response_data["raw_data"] = profile_data["raw_data"]

        logger.info(f"用户画像查询成功: {user_id}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询用户画像失败: {str(e)}"
        )

@app.post("/batch_user_profiles")
async def batch_get_user_profiles(
    user_ids: list[str],
    api_key: str = Depends(verify_api_key),
    include_raw_data: bool = False
):
    """
    批量获取用户画像信息

    Args:
        user_ids: 用户ID列表
        include_raw_data: 是否包含原始数据

    Returns:
        批量用户画像信息
    """
    try:
        if len(user_ids) > 50:  # 限制批量查询数量
            raise HTTPException(status_code=400, detail="批量查询用户数量不能超过50个")

        results = []
        for user_id in user_ids:
            try:
                profile_data = profile_generator.get_user_profile(user_id)
                if "error" not in profile_data:
                    result = {
                        "user_id": user_id,
                        "user_profile_description": profile_data["user_profile_description"],
                        "status": "success"
                    }
                    if include_raw_data:
                        result["raw_data"] = profile_data["raw_data"]
                else:
                    result = {
                        "user_id": user_id,
                        "error": profile_data["error"],
                        "status": "error"
                    }
                results.append(result)
            except Exception as e:
                results.append({
                    "user_id": user_id,
                    "error": str(e),
                    "status": "error"
                })

        return {
            "results": results,
            "total_count": len(user_ids),
            "success_count": len([r for r in results if r["status"] == "success"]),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量查询用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量查询失败: {str(e)}"
        )

@app.get("/profile/me")
async def get_my_profile(
    current_user: dict = Depends(get_current_user),
    include_raw_data: bool = False
):
    """
    获取当前认证用户的画像信息（SSO认证方式）
    """
    try:
        user_id = current_user.get('user_id')
        if not user_id:
            raise HTTPException(status_code=400, detail="无法获取用户ID")

        logger.info(f"正在查询当前用户画像: {user_id}")

        # 获取用户画像
        profile_data = profile_generator.get_user_profile(user_id)

        if "error" in profile_data:
            raise HTTPException(status_code=404, detail=profile_data["error"])

        # 构建响应数据
        response_data = {
            "user_id": user_id,
            "user_profile_description": profile_data["user_profile_description"],
            "auth_info": {
                "username": current_user.get('username'),
                "real_name": current_user.get('real_name'),
                "email": current_user.get('email')
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

        # 如果需要包含原始数据
        if include_raw_data:
            response_data["raw_data"] = profile_data["raw_data"]

        logger.info(f"当前用户画像查询成功: {user_id}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询当前用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询用户画像失败: {str(e)}"
        )

@app.get("/profile/{target_user_id}")
async def get_user_profile_by_id(
    target_user_id: str,
    current_user: dict = Depends(require_roles(["admin", "teacher"])),
    include_raw_data: bool = False
):
    """
    根据用户ID获取指定用户的画像信息（需要管理员或教师权限）
    """
    try:
        logger.info(f"管理员 {current_user.get('user_id')} 正在查询用户画像: {target_user_id}")

        # 获取用户画像
        profile_data = profile_generator.get_user_profile(target_user_id)

        if "error" in profile_data:
            raise HTTPException(status_code=404, detail=profile_data["error"])

        # 构建响应数据
        response_data = {
            "user_id": target_user_id,
            "user_profile_description": profile_data["user_profile_description"],
            "queried_by": {
                "user_id": current_user.get('user_id'),
                "username": current_user.get('username')
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

        # 如果需要包含原始数据
        if include_raw_data:
            response_data["raw_data"] = profile_data["raw_data"]

        logger.info(f"用户画像查询成功: {target_user_id}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询指定用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询用户画像失败: {str(e)}"
        )

@app.get("/auth/userinfo")
async def get_user_info(current_user: dict = Depends(get_current_user)):
    """
    获取当前认证用户的基本信息
    """
    return {
        "user_id": current_user.get('user_id'),
        "username": current_user.get('username'),
        "real_name": current_user.get('real_name'),
        "email": current_user.get('email'),
        "roles": current_user.get('roles', []),
        "departments": current_user.get('departments', []),
        "token_type": current_user.get('token_type'),
        "timestamp": datetime.now().isoformat()
    }

# ==================== 配置界面路由 ====================

@app.get("/config", response_class=HTMLResponse)
async def config_interface():
    """配置界面"""
    # 读取静态HTML文件内容
    try:
        with open("config.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
        <head><title>配置界面</title></head>
        <body>
        <h1>配置界面文件未找到</h1>
        <p>请确保 config.html 文件存在于项目根目录</p>
        </body>
        </html>
        """, status_code=404)

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径重定向到配置界面"""
    return HTMLResponse(content="""
    <html>
    <head>
        <title>智能体个性化学习系统</title>
        <meta charset="UTF-8">
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .container { max-width: 600px; margin: 0 auto; }
            .btn { display: inline-block; padding: 12px 24px; margin: 10px;
                   background: #007bff; color: white; text-decoration: none;
                   border-radius: 5px; }
            .btn:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 智能体个性化学习系统</h1>
            <p>欢迎使用智能体个性化学习系统！</p>

            <div>
                <a href="/config" class="btn">📱 配置界面</a>
                <a href="/docs" class="btn">📚 API文档</a>
                <a href="/health" class="btn">🔍 健康检查</a>
            </div>

            <div style="margin-top: 30px;">
                <h3>🚀 快速开始</h3>
                <ol style="text-align: left; max-width: 400px; margin: 0 auto;">
                    <li>点击"配置界面"进行系统配置</li>
                    <li>配置数据库连接和身份认证</li>
                    <li>设置Dify对接参数</li>
                    <li>开始使用个性化学习服务</li>
                </ol>
            </div>

            <div style="margin-top: 30px; color: #666;">
                <p>版本: v1.0.0 | <a href="https://github.com/nacoo/smartstudio">GitHub</a></p>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/config", response_class=HTMLResponse)
async def config_interface():
    """配置界面"""
    # 读取静态HTML文件内容
    try:
        with open("config.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
        <head><title>配置界面</title></head>
        <body>
        <h1>配置界面文件未找到</h1>
        <p>请确保 config.html 文件存在于项目根目录</p>
        </body>
        </html>
        """, status_code=404)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

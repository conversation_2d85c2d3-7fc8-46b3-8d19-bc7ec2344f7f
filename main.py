"""
智能体个性化学习系统 - FastAPI服务
支持统一身份认证和多维度用户画像查询
"""

import os
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from fastapi import FastAPI, HTTPException, Security, Depends, Request, Form, UploadFile, File
from fastapi.security import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import mysql.connector
from mysql.connector import Error
import json
import shutil

# 导入身份认证模块
from auth_integration import get_current_user, get_optional_user, require_roles

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 环境变量配置
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "3306")
DB_NAME = os.getenv("DB_NAME", "intelligent_agent_db")
DB_USER = os.getenv("DB_USER", "root")
DB_PASSWORD = os.getenv("DB_PASSWORD", "password")

# API安全配置
API_KEY = os.getenv("API_KEY", "dify-agent-secret-key-2024")

# FastAPI应用实例
app = FastAPI(
    title="智能体个性化学习系统API",
    description="为Dify智能体提供用户画像查询服务，支持统一身份认证和多维度用户信息整合",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建必要的目录
os.makedirs("static", exist_ok=True)
os.makedirs("templates", exist_ok=True)
os.makedirs("public", exist_ok=True)

# 静态文件和模板配置
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/public", StaticFiles(directory="public"), name="public")
templates = Jinja2Templates(directory="templates")

# API密钥验证
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)

def verify_api_key(api_key: str = Security(api_key_header)):
    """验证API密钥"""
    if api_key != API_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid API key"
        )
    return api_key

# 数据库连接管理
class DatabaseManager:
    def __init__(self):
        self.connection_config = {
            'host': DB_HOST,
            'port': int(DB_PORT),
            'database': DB_NAME,
            'user': DB_USER,
            'password': DB_PASSWORD,
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = mysql.connector.connect(**self.connection_config)
            if connection.is_connected():
                return connection
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            raise HTTPException(status_code=500, detail="数据库连接失败")
        return None
    
    def close_connection(self, connection):
        """关闭数据库连接"""
        if connection and connection.is_connected():
            connection.close()

db_manager = DatabaseManager()

class UserProfileGenerator:
    """用户画像生成器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """获取完整的用户画像信息"""
        connection = None
        try:
            connection = self.db_manager.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # 获取用户基本信息
            basic_info = self._get_basic_info(cursor, user_id)
            if not basic_info:
                return {"error": "用户不存在"}
            
            # 获取学生信息
            student_info = self._get_student_info(cursor, user_id)
            
            # 获取学习能力评估
            assessments = self._get_learning_assessments(cursor, user_id)
            
            # 获取学习记录
            learning_records = self._get_learning_records(cursor, user_id)
            
            # 获取兴趣偏好
            interests = self._get_user_interests(cursor, user_id)
            
            # 获取学习偏好
            preferences = self._get_learning_preferences(cursor, user_id)
            
            # 获取个性特征
            personality = self._get_personality_traits(cursor, user_id)
            
            # 生成格式化的用户画像描述
            profile_description = self._generate_profile_description({
                'basic_info': basic_info,
                'student_info': student_info,
                'assessments': assessments,
                'learning_records': learning_records,
                'interests': interests,
                'preferences': preferences,
                'personality': personality
            })
            
            return {
                "user_id": user_id,
                "user_profile_description": profile_description,
                "raw_data": {
                    "basic_info": basic_info,
                    "student_info": student_info,
                    "assessments": assessments,
                    "learning_records": learning_records,
                    "interests": interests,
                    "preferences": preferences,
                    "personality": personality
                }
            }
            
        except Error as e:
            logger.error(f"查询用户画像失败: {e}")
            raise HTTPException(status_code=500, detail="查询用户画像失败")
        finally:
            if connection:
                self.db_manager.close_connection(connection)
    
    def _get_basic_info(self, cursor, user_id: str):
        """获取用户基本信息"""
        cursor.execute(
            "SELECT * FROM users WHERE id = %s",
            (user_id,)
        )
        return cursor.fetchone()
    
    def _get_student_info(self, cursor, user_id: str):
        """获取学生信息"""
        cursor.execute(
            "SELECT * FROM student_profiles WHERE user_id = %s",
            (user_id,)
        )
        return cursor.fetchone()
    
    def _get_learning_assessments(self, cursor, user_id: str):
        """获取学习能力评估"""
        cursor.execute(
            "SELECT * FROM learning_assessments WHERE user_id = %s ORDER BY assessment_date DESC",
            (user_id,)
        )
        return cursor.fetchall()
    
    def _get_learning_records(self, cursor, user_id: str):
        """获取学习记录"""
        cursor.execute(
            "SELECT * FROM learning_records WHERE user_id = %s ORDER BY last_study_date DESC",
            (user_id,)
        )
        return cursor.fetchall()
    
    def _get_user_interests(self, cursor, user_id: str):
        """获取用户兴趣"""
        cursor.execute(
            "SELECT * FROM user_interests WHERE user_id = %s ORDER BY interest_level DESC",
            (user_id,)
        )
        return cursor.fetchall()
    
    def _get_learning_preferences(self, cursor, user_id: str):
        """获取学习偏好"""
        cursor.execute(
            "SELECT * FROM learning_preferences WHERE user_id = %s",
            (user_id,)
        )
        return cursor.fetchone()
    
    def _get_personality_traits(self, cursor, user_id: str):
        """获取个性特征"""
        cursor.execute(
            "SELECT * FROM personality_traits WHERE user_id = %s ORDER BY confidence_level DESC",
            (user_id,)
        )
        return cursor.fetchall()

    def _generate_profile_description(self, data: Dict[str, Any]) -> str:
        """生成格式化的用户画像描述"""
        basic_info = data.get('basic_info', {})
        student_info = data.get('student_info', {})
        assessments = data.get('assessments', [])
        learning_records = data.get('learning_records', [])
        interests = data.get('interests', [])
        preferences = data.get('preferences', {})
        personality = data.get('personality', [])

        # 构建用户画像描述
        description_parts = []

        # 基本信息部分
        if basic_info:
            name = basic_info.get('real_name', '该用户')
            gender = basic_info.get('gender', '')
            age = self._calculate_age(basic_info.get('birth_date')) if basic_info.get('birth_date') else None

            basic_desc = f"用户姓名：{name}"
            if gender:
                basic_desc += f"，性别：{gender}"
            if age:
                basic_desc += f"，年龄：{age}岁"
            description_parts.append(basic_desc + "。")

        # 学生信息部分
        if student_info:
            student_desc = f"学生信息：{student_info.get('grade_level', '')}学生"
            if student_info.get('major'):
                student_desc += f"，专业为{student_info.get('major')}"
            if student_info.get('class_name'):
                student_desc += f"，就读于{student_info.get('class_name')}"
            description_parts.append(student_desc + "。")

        # 学习能力评估部分
        if assessments:
            assessment_desc = "学习能力评估："
            strong_subjects = []
            weak_subjects = []

            for assessment in assessments[:3]:  # 取最近3个评估
                subject = assessment.get('subject', '')
                level = assessment.get('skill_level', '')
                score = assessment.get('assessment_score', 0)

                if score >= 80:
                    strong_subjects.append(f"{subject}({level})")
                elif score < 70:
                    weak_subjects.append(f"{subject}({level})")

            if strong_subjects:
                assessment_desc += f"擅长{', '.join(strong_subjects)}"
            if weak_subjects:
                if strong_subjects:
                    assessment_desc += f"，需要加强{', '.join(weak_subjects)}"
                else:
                    assessment_desc += f"需要加强{', '.join(weak_subjects)}"

            description_parts.append(assessment_desc + "。")

        # 学习记录部分
        if learning_records:
            current_courses = []
            for record in learning_records[:3]:  # 取最近3门课程
                course_name = record.get('course_name', '')
                completion_rate = record.get('completion_rate', 0)
                progress = record.get('current_progress', '')

                if completion_rate > 0:
                    current_courses.append(f"{course_name}(完成度{completion_rate}%)")

            if current_courses:
                learning_desc = f"当前学习课程：{', '.join(current_courses)}"
                description_parts.append(learning_desc + "。")

        # 兴趣偏好部分
        if interests:
            high_interests = [interest.get('interest_name', '')
                            for interest in interests
                            if interest.get('interest_level', 0) >= 4][:5]

            if high_interests:
                interest_desc = f"兴趣爱好：对{', '.join(high_interests)}特别感兴趣"
                description_parts.append(interest_desc + "。")

        # 学习偏好部分
        if preferences:
            pref_desc = "学习偏好："
            learning_style = preferences.get('learning_style', '')
            preferred_pace = preferences.get('preferred_pace', '')
            interaction_style = preferences.get('interaction_style', '')

            style_map = {
                'visual': '视觉学习者',
                'auditory': '听觉学习者',
                'kinesthetic': '动手实践者',
                'reading': '阅读学习者'
            }

            pace_map = {
                'slow': '喜欢慢节奏学习',
                'normal': '适应正常节奏',
                'fast': '喜欢快节奏学习'
            }

            interaction_map = {
                'formal': '偏好正式的交流方式',
                'casual': '喜欢轻松的交流氛围',
                'encouraging': '需要鼓励和支持'
            }

            pref_parts = []
            if learning_style in style_map:
                pref_parts.append(style_map[learning_style])
            if preferred_pace in pace_map:
                pref_parts.append(pace_map[preferred_pace])
            if interaction_style in interaction_map:
                pref_parts.append(interaction_map[interaction_style])

            if pref_parts:
                pref_desc += '，'.join(pref_parts)
                description_parts.append(pref_desc + "。")

        # 个性特征部分
        if personality:
            personality_desc = "个性特征："
            traits = []
            for trait in personality[:3]:  # 取置信度最高的3个特征
                trait_name = trait.get('trait_name', '')
                trait_value = trait.get('trait_value', '')
                if trait_name and trait_value:
                    traits.append(f"{trait_name}方面{trait_value}")

            if traits:
                personality_desc += '；'.join(traits)
                description_parts.append(personality_desc + "。")

        # 组合所有描述
        full_description = '\n'.join(description_parts)

        # 添加指导建议
        guidance = self._generate_guidance_suggestions(data)
        if guidance:
            full_description += f"\n\n教学建议：{guidance}"

        return full_description

    def _calculate_age(self, birth_date) -> Optional[int]:
        """计算年龄"""
        if not birth_date:
            return None
        try:
            today = datetime.now().date()
            return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
        except:
            return None

    def _generate_guidance_suggestions(self, data: Dict[str, Any]) -> str:
        """生成教学指导建议"""
        suggestions = []

        # 基于学习偏好的建议
        preferences = data.get('preferences', {})
        if preferences:
            learning_style = preferences.get('learning_style', '')
            if learning_style == 'visual':
                suggestions.append("多使用图表、图像和视觉化工具进行教学")
            elif learning_style == 'auditory':
                suggestions.append("可以通过讲解、讨论和音频材料进行教学")
            elif learning_style == 'kinesthetic':
                suggestions.append("安排更多实践操作和动手练习")
            elif learning_style == 'reading':
                suggestions.append("提供详细的文字资料和阅读材料")

        # 基于学习能力的建议
        assessments = data.get('assessments', [])
        if assessments:
            weak_areas = [a.get('subject') for a in assessments if a.get('assessment_score', 0) < 70]
            if weak_areas:
                suggestions.append(f"重点关注{', '.join(weak_areas[:2])}等薄弱环节")

        # 基于个性特征的建议
        personality = data.get('personality', [])
        for trait in personality:
            trait_name = trait.get('trait_name', '')
            trait_value = trait.get('trait_value', '')
            if '自信心不足' in trait_value:
                suggestions.append("多给予鼓励和正面反馈，建立学习自信心")
            elif '追求完美' in trait_value:
                suggestions.append("引导其接受渐进式学习，避免完美主义压力")

        return '；'.join(suggestions[:3]) if suggestions else ""

# 创建用户画像生成器实例
profile_generator = UserProfileGenerator(db_manager)

# API端点定义

@app.get("/", response_class=HTMLResponse)
async def root():
    """学生学情分析工具 - 主页面重定向到登录"""
    return HTMLResponse(content="""
    <html>
    <head>
        <title>学生学情分析工具</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
            }
            .welcome-card {
                background: white;
                border-radius: 15px;
                padding: 40px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                max-width: 500px;
                width: 100%;
                text-align: center;
            }
            .btn {
                display: inline-block;
                padding: 15px 30px;
                margin: 10px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-decoration: none;
                border-radius: 25px;
                font-size: 16px;
                transition: all 0.3s;
            }
            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }
            .admin-link {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
            }
            .admin-link a {
                color: #666;
                text-decoration: none;
                font-size: 14px;
            }
            .admin-link a:hover {
                color: #333;
            }
        </style>
    </head>
    <body>
        <div class="welcome-card">
            <h1>📚 学生学情分析工具</h1>
            <p style="color: #666; margin-bottom: 30px;">
                基于个人学习情况提供智能化分析和指导建议
            </p>

            <a href="/login" class="btn">🚀 开始使用</a>

            <div style="margin-top: 20px; color: #999; font-size: 12px;">
                版本 v1.2.0 | NacooLab
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 测试数据库连接
        connection = db_manager.get_connection()
        if connection:
            db_manager.close_connection(connection)
            db_status = "healthy"
        else:
            db_status = "unhealthy"
    except Exception as e:
        db_status = f"error: {str(e)}"

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database": db_status,
        "version": "1.0.0"
    }

@app.get("/get_user_profile")
async def get_user_profile(
    user_id: str,
    api_key: str = Depends(verify_api_key),
    include_raw_data: bool = False
):
    """
    获取用户画像信息（API Key认证方式，用于Dify调用）
    """
    """
    获取用户画像信息

    Args:
        user_id: 用户ID（来自统一身份认证系统）
        include_raw_data: 是否包含原始数据（默认False，仅返回格式化描述）

    Returns:
        用户画像描述和相关信息
    """
    try:
        logger.info(f"正在查询用户画像: {user_id}")

        # 获取用户画像
        profile_data = profile_generator.get_user_profile(user_id)

        if "error" in profile_data:
            raise HTTPException(status_code=404, detail=profile_data["error"])

        # 构建响应数据
        response_data = {
            "user_id": user_id,
            "user_profile_description": profile_data["user_profile_description"],
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

        # 如果需要包含原始数据
        if include_raw_data:
            response_data["raw_data"] = profile_data["raw_data"]

        logger.info(f"用户画像查询成功: {user_id}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询用户画像失败: {str(e)}"
        )

@app.post("/batch_user_profiles")
async def batch_get_user_profiles(
    user_ids: list[str],
    api_key: str = Depends(verify_api_key),
    include_raw_data: bool = False
):
    """
    批量获取用户画像信息

    Args:
        user_ids: 用户ID列表
        include_raw_data: 是否包含原始数据

    Returns:
        批量用户画像信息
    """
    try:
        if len(user_ids) > 50:  # 限制批量查询数量
            raise HTTPException(status_code=400, detail="批量查询用户数量不能超过50个")

        results = []
        for user_id in user_ids:
            try:
                profile_data = profile_generator.get_user_profile(user_id)
                if "error" not in profile_data:
                    result = {
                        "user_id": user_id,
                        "user_profile_description": profile_data["user_profile_description"],
                        "status": "success"
                    }
                    if include_raw_data:
                        result["raw_data"] = profile_data["raw_data"]
                else:
                    result = {
                        "user_id": user_id,
                        "error": profile_data["error"],
                        "status": "error"
                    }
                results.append(result)
            except Exception as e:
                results.append({
                    "user_id": user_id,
                    "error": str(e),
                    "status": "error"
                })

        return {
            "results": results,
            "total_count": len(user_ids),
            "success_count": len([r for r in results if r["status"] == "success"]),
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量查询用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量查询失败: {str(e)}"
        )

@app.get("/profile/me")
async def get_my_profile(
    current_user: dict = Depends(get_current_user),
    include_raw_data: bool = False
):
    """
    获取当前认证用户的画像信息（SSO认证方式）
    """
    try:
        user_id = current_user.get('user_id')
        if not user_id:
            raise HTTPException(status_code=400, detail="无法获取用户ID")

        logger.info(f"正在查询当前用户画像: {user_id}")

        # 获取用户画像
        profile_data = profile_generator.get_user_profile(user_id)

        if "error" in profile_data:
            raise HTTPException(status_code=404, detail=profile_data["error"])

        # 构建响应数据
        response_data = {
            "user_id": user_id,
            "user_profile_description": profile_data["user_profile_description"],
            "auth_info": {
                "username": current_user.get('username'),
                "real_name": current_user.get('real_name'),
                "email": current_user.get('email')
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

        # 如果需要包含原始数据
        if include_raw_data:
            response_data["raw_data"] = profile_data["raw_data"]

        logger.info(f"当前用户画像查询成功: {user_id}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询当前用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询用户画像失败: {str(e)}"
        )

@app.get("/profile/{target_user_id}")
async def get_user_profile_by_id(
    target_user_id: str,
    current_user: dict = Depends(require_roles(["admin", "teacher"])),
    include_raw_data: bool = False
):
    """
    根据用户ID获取指定用户的画像信息（需要管理员或教师权限）
    """
    try:
        logger.info(f"管理员 {current_user.get('user_id')} 正在查询用户画像: {target_user_id}")

        # 获取用户画像
        profile_data = profile_generator.get_user_profile(target_user_id)

        if "error" in profile_data:
            raise HTTPException(status_code=404, detail=profile_data["error"])

        # 构建响应数据
        response_data = {
            "user_id": target_user_id,
            "user_profile_description": profile_data["user_profile_description"],
            "queried_by": {
                "user_id": current_user.get('user_id'),
                "username": current_user.get('username')
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

        # 如果需要包含原始数据
        if include_raw_data:
            response_data["raw_data"] = profile_data["raw_data"]

        logger.info(f"用户画像查询成功: {target_user_id}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询指定用户画像时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"查询用户画像失败: {str(e)}"
        )

@app.get("/auth/userinfo")
async def get_user_info(current_user: dict = Depends(get_current_user)):
    """
    获取当前认证用户的基本信息
    """
    return {
        "user_id": current_user.get('user_id'),
        "username": current_user.get('username'),
        "real_name": current_user.get('real_name'),
        "email": current_user.get('email'),
        "roles": current_user.get('roles', []),
        "departments": current_user.get('departments', []),
        "token_type": current_user.get('token_type'),
        "timestamp": datetime.now().isoformat()
    }

# ==================== 配置界面路由 ====================

@app.get("/config", response_class=HTMLResponse)
async def config_interface():
    """配置界面"""
    # 读取静态HTML文件内容
    try:
        with open("config.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
        <head><title>配置界面</title></head>
        <body>
        <h1>配置界面文件未找到</h1>
        <p>请确保 config.html 文件存在于项目根目录</p>
        </body>
        </html>
        """, status_code=404)




@app.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(request: Request):
    """系统管理后台 - 需要身份认证"""
    # 检查管理员认证
    admin_token = request.cookies.get("admin_token")
    if not admin_token or admin_token != "admin-authenticated-2024":
        # 重定向到管理员登录页面
        return HTMLResponse(content="""
        <html>
        <head>
            <title>管理员登录</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0;
                }
                .login-card {
                    background: white;
                    border-radius: 15px;
                    padding: 40px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    max-width: 400px;
                    width: 100%;
                }
                .form-group {
                    margin-bottom: 20px;
                }
                .form-group label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                }
                .form-group input {
                    width: 100%;
                    padding: 12px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    box-sizing: border-box;
                }
                .btn {
                    width: 100%;
                    padding: 12px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 16px;
                }
                .btn:hover {
                    opacity: 0.9;
                }
                .error {
                    color: red;
                    margin-top: 10px;
                    display: none;
                }
                .back-link {
                    text-align: center;
                    margin-top: 20px;
                }
                .back-link a {
                    color: #666;
                    text-decoration: none;
                    font-size: 14px;
                }
            </style>
        </head>
        <body>
            <div class="login-card">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h2>🔐 管理员登录</h2>
                    <p style="color: #666;">系统管理后台访问</p>
                </div>

                <form id="adminLoginForm">
                    <div class="form-group">
                        <label for="admin_username">管理员账号</label>
                        <input type="text" id="admin_username" name="admin_username" required>
                    </div>

                    <div class="form-group">
                        <label for="admin_password">管理员密码</label>
                        <input type="password" id="admin_password" name="admin_password" required>
                    </div>

                    <button type="submit" class="btn">登录管理后台</button>

                    <div class="error" id="error-message"></div>
                </form>

                <div class="back-link">
                    <a href="/">← 返回主页</a>
                </div>
            </div>

            <script>
                document.getElementById('adminLoginForm').addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const username = document.getElementById('admin_username').value;
                    const password = document.getElementById('admin_password').value;
                    const errorDiv = document.getElementById('error-message');

                    try {
                        const response = await fetch('/admin/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                // 设置认证cookie
                                document.cookie = `admin_token=${data.token}; path=/; max-age=3600`;
                                window.location.reload();
                            } else {
                                errorDiv.textContent = data.message || '登录失败';
                                errorDiv.style.display = 'block';
                            }
                        } else {
                            errorDiv.textContent = '登录失败，请检查账号密码';
                            errorDiv.style.display = 'block';
                        }
                    } catch (error) {
                        errorDiv.textContent = '登录过程中发生错误';
                        errorDiv.style.display = 'block';
                    }
                });
            </script>
        </body>
        </html>
        """)

    # 已认证，显示管理后台
    return HTMLResponse(content="""
    <html>
    <head>
        <title>系统管理后台</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
            }
            .admin-card {
                background: white;
                border-radius: 15px;
                padding: 40px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                max-width: 600px;
                width: 100%;
                text-align: center;
            }
            .btn {
                display: inline-block;
                padding: 15px 25px;
                margin: 10px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-decoration: none;
                border-radius: 10px;
                font-size: 14px;
                transition: all 0.3s;
                min-width: 150px;
            }
            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }
            .btn-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 15px;
                margin: 30px 0;
            }
            .back-link {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
            }
            .back-link a {
                color: #666;
                text-decoration: none;
                font-size: 14px;
            }
            .back-link a:hover {
                color: #333;
            }
        </style>
    </head>
    <body>
        <div class="admin-card">
            <h1>🔧 系统管理后台</h1>
            <p style="color: #666; margin-bottom: 30px;">
                智能体个性化学习系统管理控制台
            </p>

            <div class="btn-grid">
                <a href="/config" class="btn">📱 配置界面</a>
                <a href="/docs" class="btn">📚 API文档</a>
                <a href="/health" class="btn">🔍 健康检查</a>
                <a href="/get_user_profile?user_id=user001" class="btn">👤 测试API</a>
                <button onclick="adminLogout()" class="btn" style="background: #dc3545;">🚪 安全登出</button>
            </div>

            <div class="back-link">
                <a href="/">← 返回学生工具</a>
            </div>

            <script>
                async function adminLogout() {
                    try {
                        const response = await fetch('/admin/logout', {
                            method: 'POST'
                        });
                        if (response.ok) {
                            document.cookie = 'admin_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                            window.location.href = '/';
                        }
                    } catch (error) {
                        alert('登出失败');
                    }
                }
            </script>

            <div style="margin-top: 20px; color: #999; font-size: 12px;">
                系统管理 v1.2.0 | NacooLab
            </div>
        </div>
    </body>
    </html>
    """)

@app.post("/admin/login")
async def admin_login(username: str = Form(...), password: str = Form(...)):
    """管理员登录验证"""
    # 管理员凭据验证（在实际环境中应该使用更安全的方式）
    ADMIN_USERNAME = "admin"
    ADMIN_PASSWORD = "NacooLab@2024"

    if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
        return {
            "success": True,
            "token": "admin-authenticated-2024",
            "message": "管理员登录成功"
        }
    else:
        raise HTTPException(status_code=401, detail="管理员账号或密码错误")

@app.post("/admin/logout")
async def admin_logout():
    """管理员登出"""
    response = JSONResponse({"success": True, "message": "已安全登出"})
    response.delete_cookie("admin_token")
    return response

# ==================== 身份认证相关路由 ====================

@app.get("/auth/user")
async def get_current_user():
    """获取当前用户信息（演示版本）"""
    # 在实际环境中，这里应该从JWT token或session中获取用户信息
    return {
        "user_id": "user_001",
        "username": "张三",
        "email": "<EMAIL>",
        "authenticated": True,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/auth/login")
async def login(username: str, password: str):
    """用户登录（演示版本）"""
    # 在实际环境中，这里应该验证用户凭据
    if username and password:
        return {
            "success": True,
            "user_id": "user_001",
            "username": username,
            "token": "demo-jwt-token",
            "message": "登录成功"
        }
    else:
        raise HTTPException(status_code=401, detail="用户名或密码错误")

@app.get("/login", response_class=HTMLResponse)
async def login_page():
    """学生登录页面"""
    return HTMLResponse(content="""
    <html>
    <head>
        <title>学生登录 - 学生学情分析工具</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-card {
                background: white;
                border-radius: 15px;
                padding: 40px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                max-width: 400px;
                width: 100%;
            }
        </style>
    </head>
    <body>
        <div class="login-card">
            <div class="text-center mb-4">
                <h2>📚 学生学情分析工具</h2>
                <p class="text-muted">请登录以获取个性化学习分析</p>
            </div>

            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" value="demo_user" required>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" value="demo_pass" required>
                </div>

                <button type="submit" class="btn btn-primary w-100">登录</button>
            </form>

            <div class="text-center mt-3">
                <small class="text-muted">演示账号: demo_user / demo_pass</small>
            </div>
        </div>

        <script>
            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                try {
                    const response = await fetch('/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                    });

                    if (response.ok) {
                        const data = await response.json();
                        localStorage.setItem('user_token', data.token);
                        localStorage.setItem('user_id', data.user_id);
                        window.location.href = '/chat';
                    } else {
                        alert('登录失败，请检查用户名和密码');
                    }
                } catch (error) {
                    alert('登录过程中发生错误');
                }
            });
        </script>
    </body>
    </html>
    """)

# ==================== 智能体对话界面路由 ====================

@app.get("/chat", response_class=HTMLResponse)
async def chat_interface(request: Request):
    """智能体对话界面"""
    # 读取Dify配置
    dify_config = {}
    try:
        with open("dify_config.json", "r", encoding="utf-8") as f:
            dify_config = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        dify_config = {
            "api_url": "",
            "api_key": "",
            "app_id": "",
            "workflow_enabled": False
        }

    # 获取用户ID (在实际环境中，这应该从认证系统获取)
    user_id = "user_001"  # 演示用户ID，实际应该从session或JWT获取

    return HTMLResponse(content=f"""
    <html>
    <head>
        <title>学情分析对话 - 学生学情分析工具</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
        <style>
            body {{
                font-family: Arial, sans-serif;
                background-color: #f8f9fa;
                margin: 0;
                padding: 0;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px;
                text-align: center;
            }}
            .chat-container {{
                flex: 1;
                display: flex;
                flex-direction: column;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                width: 100%;
            }}
            .messages {{
                flex: 1;
                overflow-y: auto;
                margin-bottom: 20px;
                padding: 15px;
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .message {{
                margin-bottom: 15px;
                padding: 10px 15px;
                border-radius: 18px;
                max-width: 80%;
                word-wrap: break-word;
            }}
            .user-message {{
                background-color: #e3f2fd;
                margin-left: auto;
                border-bottom-right-radius: 5px;
            }}
            .bot-message {{
                background-color: #f1f1f1;
                margin-right: auto;
                border-bottom-left-radius: 5px;
            }}
            .input-area {{
                display: flex;
                gap: 10px;
            }}
            .input-area input {{
                flex: 1;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 25px;
                outline: none;
            }}
            .input-area button {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 25px;
                padding: 0 20px;
                cursor: pointer;
            }}
            .input-area button:hover {{
                opacity: 0.9;
            }}
            .user-info {{
                text-align: right;
                margin-bottom: 10px;
                font-size: 0.9em;
                color: #666;
            }}
            .loading {{
                display: none;
                text-align: center;
                margin: 10px 0;
            }}
            .dot-flashing {{
                display: inline-block;
                width: 10px;
                height: 10px;
                border-radius: 5px;
                background-color: #764ba2;
                animation: dot-flashing 1s infinite linear alternate;
                animation-delay: 0.5s;
            }}
            .dot-flashing::before, .dot-flashing::after {{
                content: '';
                display: inline-block;
                position: absolute;
                top: 0;
                width: 10px;
                height: 10px;
                border-radius: 5px;
                background-color: #764ba2;
                animation: dot-flashing 1s infinite alternate;
            }}
            .dot-flashing::before {{
                left: -15px;
                animation-delay: 0s;
            }}
            .dot-flashing::after {{
                left: 15px;
                animation-delay: 1s;
            }}
            @keyframes dot-flashing {{
                0% {{ background-color: #764ba2; }}
                50%, 100% {{ background-color: #e3e3e3; }}
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h2>📚 学生学情分析工具</h2>
            <p>基于您的学习情况提供个性化分析和指导</p>
        </div>

        <div class="chat-container">
            <div class="user-info">
                <span>当前用户: <strong id="current-user">{user_id}</strong></span>
            </div>

            <div class="messages" id="messages">
                <div class="message bot-message">
                    您好！我是您的学情分析助手。我会根据您的学习情况提供个性化的分析和建议。请问有什么可以帮助您的？
                </div>
            </div>

            <div class="loading" id="loading">
                <div class="dot-flashing"></div>
            </div>

            <div class="input-area">
                <input type="text" id="user-input" placeholder="请输入您的问题..." />
                <button id="send-button">
                    <i class="bi bi-send"></i> 发送
                </button>
            </div>
        </div>

        <script>
            // 配置信息
            const config = {{
                difyApiUrl: "{dify_config.get('api_url', '')}",
                difyApiKey: "{dify_config.get('api_key', '')}",
                difyAppId: "{dify_config.get('app_id', '')}",
                userId: localStorage.getItem('user_id') || "{user_id}",
                apiEndpoint: "/get_user_profile"
            }};

            // 检查用户是否已登录
            function checkLogin() {{
                const token = localStorage.getItem('user_token');
                if (!token) {{
                    // 未登录，重定向到登录页面
                    window.location.href = '/login';
                    return false;
                }}
                return true;
            }}

            // DOM元素
            const messagesContainer = document.getElementById('messages');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const loadingIndicator = document.getElementById('loading');

            // 会话ID
            let conversationId = null;

            // 添加消息到聊天界面
            function addMessage(content, isUser) {{
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${{isUser ? 'user-message' : 'bot-message'}}`;
                messageDiv.textContent = content;
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }}

            // 显示加载指示器
            function showLoading(show) {{
                loadingIndicator.style.display = show ? 'block' : 'none';
            }}

            // 发送消息到Dify
            async function sendMessageToDify(message) {{
                if (!config.difyApiUrl || !config.difyApiKey) {{
                    addMessage("错误: Dify配置不完整。请先完成配置。", false);
                    return;
                }}

                try {{
                    showLoading(true);

                    // 获取用户画像
                    const userProfile = await fetchUserProfile(config.userId);

                    // 构建请求体
                    const requestBody = {{
                        inputs: {{}},
                        query: message,
                        response_mode: "streaming",
                        conversation_id: conversationId,
                        user: config.userId,
                        user_profile: userProfile
                    }};

                    // 发送请求到Dify
                    const response = await fetch(`${{config.difyApiUrl}}/chat-messages`, {{
                        method: 'POST',
                        headers: {{
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${{config.difyApiKey}}`
                        }},
                        body: JSON.stringify(requestBody)
                    }});

                    if (!response.ok) {{
                        throw new Error(`Dify API错误: ${{response.status}}`);
                    }}

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let botResponse = "";

                    // 处理流式响应
                    while (true) {{
                        const {{done, value}} = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value, {{stream: true}});
                        const lines = chunk.split('\\n').filter(line => line.trim() !== '');

                        for (const line of lines) {{
                            if (line.startsWith('data:')) {{
                                const data = JSON.parse(line.substring(5));

                                if (data.event === 'message') {{
                                    botResponse += data.answer || '';
                                    // 更新消息显示
                                    const botMessageDiv = document.querySelector('.bot-message:last-child');
                                    if (botMessageDiv) {{
                                        botMessageDiv.textContent = botResponse;
                                    }} else {{
                                        addMessage(botResponse, false);
                                    }}
                                }}

                                if (data.conversation_id) {{
                                    conversationId = data.conversation_id;
                                }}
                            }}
                        }}
                    }}

                    if (!botResponse) {{
                        addMessage("抱歉，我无法处理您的请求。", false);
                    }}

                }} catch (error) {{
                    console.error("Error:", error);
                    addMessage(`发生错误: ${{error.message}}`, false);
                }} finally {{
                    showLoading(false);
                }}
            }}

            // 获取用户画像
            async function fetchUserProfile(userId) {{
                try {{
                    const response = await fetch(`${{config.apiEndpoint}}?user_id=${{userId}}`, {{
                        headers: {{
                            'X-API-Key': 'dify-agent-secret-key-2024'
                        }}
                    }});

                    if (!response.ok) {{
                        throw new Error(`API错误: ${{response.status}}`);
                    }}

                    const data = await response.json();
                    return data;
                }} catch (error) {{
                    console.error("获取用户画像失败:", error);
                    return null;
                }}
            }}

            // 发送消息
            function sendMessage() {{
                const message = userInput.value.trim();
                if (!message) return;

                addMessage(message, true);
                userInput.value = '';

                sendMessageToDify(message);
            }}

            // 事件监听
            sendButton.addEventListener('click', sendMessage);
            userInput.addEventListener('keypress', (e) => {{
                if (e.key === 'Enter') sendMessage();
            }});

            // 初始化
            document.addEventListener('DOMContentLoaded', () => {{
                if (checkLogin()) {{
                    userInput.focus();
                    // 更新用户显示
                    const userId = localStorage.getItem('user_id');
                    if (userId) {{
                        document.getElementById('current-user').textContent = userId;
                    }}
                }}
            }});
        </script>
    </body>
    </html>
    """)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

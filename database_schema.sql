-- 智能体个性化学习系统数据库表结构
-- 支持统一身份认证和多维度用户画像

-- 用户基本信息表
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,  -- 统一身份认证系统的用户ID
    username VARCHAR(100) NOT NULL,
    real_name VARCHAR(100),
    email VARCHAR(255),
    phone VARCHAR(20),
    gender VARCHAR(10),
    birth_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 学生详细信息表
CREATE TABLE student_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    student_id VARCHAR(50) UNIQUE,  -- 学号
    grade_level VARCHAR(20),        -- 年级
    major VARCHAR(100),             -- 专业
    class_name VARCHAR(50),         -- 班级
    enrollment_year INT,            -- 入学年份
    current_semester INT,           -- 当前学期
    academic_status VARCHAR(20) DEFAULT 'active',  -- 学籍状态
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 学习能力评估表
CREATE TABLE learning_assessments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    subject VARCHAR(100),           -- 学科
    skill_level VARCHAR(20),        -- 技能水平 (beginner/intermediate/advanced)
    assessment_score DECIMAL(5,2),  -- 评估分数
    assessment_date DATE,
    assessment_type VARCHAR(50),    -- 评估类型
    strengths TEXT,                 -- 优势描述
    weaknesses TEXT,                -- 薄弱环节
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 学习记录表
CREATE TABLE learning_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    course_name VARCHAR(200),
    course_code VARCHAR(50),
    completion_rate DECIMAL(5,2),   -- 完成率
    current_progress VARCHAR(100),  -- 当前进度
    study_hours DECIMAL(6,2),       -- 学习时长
    last_study_date DATE,
    performance_score DECIMAL(5,2), -- 表现分数
    difficulty_level VARCHAR(20),   -- 难度等级
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 兴趣偏好表
CREATE TABLE user_interests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    interest_category VARCHAR(100), -- 兴趣类别
    interest_name VARCHAR(200),     -- 具体兴趣
    interest_level INT DEFAULT 1,   -- 兴趣程度 1-5
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 学习风格偏好表
CREATE TABLE learning_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    learning_style VARCHAR(50),     -- 学习风格 (visual/auditory/kinesthetic/reading)
    preferred_pace VARCHAR(20),     -- 偏好节奏 (slow/normal/fast)
    preferred_time VARCHAR(50),     -- 偏好时间段
    interaction_style VARCHAR(50),  -- 互动风格 (formal/casual/encouraging)
    feedback_preference VARCHAR(50), -- 反馈偏好
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 个性特征表
CREATE TABLE personality_traits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    trait_name VARCHAR(100),        -- 特征名称
    trait_value VARCHAR(200),       -- 特征值/描述
    confidence_level DECIMAL(3,2),  -- 置信度
    source VARCHAR(100),            -- 数据来源
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX idx_users_id ON users(id);
CREATE INDEX idx_student_profiles_user_id ON student_profiles(user_id);
CREATE INDEX idx_learning_assessments_user_id ON learning_assessments(user_id);
CREATE INDEX idx_learning_records_user_id ON learning_records(user_id);
CREATE INDEX idx_user_interests_user_id ON user_interests(user_id);
CREATE INDEX idx_learning_preferences_user_id ON learning_preferences(user_id);
CREATE INDEX idx_personality_traits_user_id ON personality_traits(user_id);

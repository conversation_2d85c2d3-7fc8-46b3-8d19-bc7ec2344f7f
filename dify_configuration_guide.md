# Dify智能体配置指南

本指南将详细说明如何在Dify中配置智能体，实现基于统一身份认证的个性化学习系统。

## 前置条件

1. 已部署并运行用户画像API服务
2. 已配置数据库并导入示例数据
3. 已获取Dify平台访问权限
4. 已配置统一身份认证系统

## 第一步：创建HTTP工具

### 1.1 登录Dify并创建工具

1. 登录Dify平台
2. 进入 **工作室 -> 工具**
3. 点击 **创建工具**，选择 **HTTP** 类型

### 1.2 配置工具基本信息

- **工具名称**: `获取用户画像`
- **工具标识**: `get_user_profile`
- **工具描述**: 
  ```
  根据用户ID从数据库获取完整的用户画像信息，包括学习能力、兴趣偏好、个性特征等，用于生成个性化的教学指导建议。该工具会返回格式化的用户描述，可直接用作系统提示词。
  ```

### 1.3 配置API端点

**请求配置**:
- **HTTP方法**: `GET`
- **URL**: `http://your-api-server:8000/get_user_profile`
- **请求头**:
  ```json
  {
    "X-API-Key": "dify-agent-secret-key-2024",
    "Content-Type": "application/json"
  }
  ```

**参数配置**:
- **输入参数**:
  - 参数名: `user_id`
  - 参数类型: `string`
  - 是否必填: `是`
  - 参数描述: `需要查询的用户唯一标识符，通常来自统一身份认证系统`

**输出配置**:
- **输出参数**:
  - 参数名: `user_profile_description`
  - 参数类型: `string`
  - 参数描述: `格式化的用户画像描述文本，包含学习能力、偏好和个性特征`

### 1.4 测试工具

在工具配置页面点击 **测试** 按钮，使用示例用户ID（如 `user_001`）进行测试，确保能正确返回用户画像信息。

## 第二步：创建智能体应用

### 2.1 创建新应用

1. 进入 **工作室 -> 创建应用**
2. 选择 **智能体（Agent）** 类型
3. 填写应用基本信息：
   - **应用名称**: `个性化学习助手`
   - **应用描述**: `基于用户画像的智能学习指导系统`

### 2.2 配置智能体工具

1. 在智能体配置页面，找到 **工具** 部分
2. 点击 **添加工具**
3. 选择刚才创建的 `获取用户画像` 工具
4. 确认添加

## 第三步：配置提示词和工作流

### 3.1 设计系统提示词（Preamble）

在 **提示词编排** 页面的 **Preamble** 部分输入以下内容：

```
# 角色定义
你是一个专业的个性化学习助手，具备深度的教育心理学知识和丰富的教学经验。

# 重要上下文信息
以下是当前用户的详细画像信息，这些信息对用户是完全隐藏的，但你必须在每次回答中都要参考这些信息来提供个性化的指导：

---
{{#user_profile.user_profile_description#}}
---

# 核心任务
1. 基于用户画像信息，深度理解用户的学习特点、能力水平和个性偏好
2. 针对用户的具体问题，结合其学习背景和特征，提供精准的个性化建议
3. 采用适合用户学习风格的沟通方式和教学方法
4. 关注用户的薄弱环节，提供针对性的改进建议
5. 根据用户的兴趣爱好，设计更有吸引力的学习方案

# 回答原则
- 始终保持专业、耐心、鼓励的态度
- 根据用户的学习偏好调整解释方式和深度
- 对于用户的薄弱环节，提供循序渐进的指导
- 结合用户的兴趣点，让学习更有趣味性
- 绝不透露用户画像信息的存在

# 特别注意
- 用户画像信息是隐藏的，用户不知道你拥有这些信息
- 要自然地体现个性化，而不是明确说明你了解用户的具体情况
- 根据用户的交互风格偏好调整你的回答风格
```

### 3.2 配置工作流编排

1. 在 **编排** 画布中，你会看到以下节点：
   - **开始** 节点
   - **大语言模型** 节点
   - **结束** 节点

2. **添加工具节点**：
   - 从左侧工具栏拖拽 `获取用户画像` 工具到画布中
   - 将其放置在 **开始** 节点和 **大语言模型** 节点之间

3. **连接节点**：
   - 将 **开始** 节点的 `sys.user_id` 输出连接到 `获取用户画像` 工具的 `user_id` 输入
   - 确保工作流为：`开始` -> `获取用户画像` -> `大语言模型` -> `结束`

4. **重命名工具节点**：
   - 选中 `获取用户画像` 工具节点
   - 在右侧配置面板中，将 **节点变量名** 设置为 `user_profile`
   - 这个名称必须与Preamble中的变量引用 `{{#user_profile.user_profile_description#}}` 保持一致

### 3.3 配置模型参数

在 **大语言模型** 节点中：
- **模型选择**: 推荐使用 `GPT-4` 或 `Claude-3.5-Sonnet`
- **温度**: `0.7`（平衡创造性和准确性）
- **最大令牌数**: `2000`
- **Top P**: `0.9`

## 第四步：测试和优化

### 4.1 功能测试

1. 在Dify应用页面点击 **预览**
2. 使用测试用户ID进行对话测试
3. 验证以下功能：
   - 用户画像是否正确获取
   - 回答是否体现个性化特征
   - 系统提示词是否对用户隐藏

### 4.2 测试场景

建议测试以下场景：
- **学习困难咨询**: 询问某个学科的学习方法
- **学习计划制定**: 请求制定学习计划
- **知识点解释**: 询问具体知识点
- **学习动机激励**: 表达学习困惑或挫折

### 4.3 优化建议

根据测试结果，可以优化：
- 调整系统提示词的表达方式
- 优化用户画像描述的格式
- 调整模型参数以获得更好的效果

## 第五步：部署和监控

### 5.1 发布应用

1. 测试完成后，点击 **发布** 按钮
2. 配置应用访问权限
3. 获取应用API密钥（如需要）

### 5.2 集成统一身份认证

如果需要与现有系统集成：
1. 配置SSO单点登录
2. 确保用户ID能正确传递给Dify
3. 测试端到端的认证流程

### 5.3 监控和维护

- 定期检查API服务状态
- 监控用户画像数据的准确性
- 根据用户反馈优化提示词
- 定期更新用户画像数据

## 故障排除

### 常见问题

1. **工具调用失败**
   - 检查API服务是否正常运行
   - 验证API密钥是否正确
   - 确认网络连接是否正常

2. **用户画像未生效**
   - 检查节点变量名是否与Preamble中的引用一致
   - 验证工作流连接是否正确
   - 确认用户ID是否有效

3. **个性化效果不明显**
   - 优化用户画像描述的详细程度
   - 调整系统提示词的指导性
   - 增加更多维度的用户特征数据

### 调试技巧

- 使用Dify的日志功能查看工具调用详情
- 在测试环境中启用详细日志
- 通过API直接测试用户画像获取功能

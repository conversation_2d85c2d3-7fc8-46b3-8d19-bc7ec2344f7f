<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体系统配置界面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .config-section {
            display: none;
        }
        .config-section.active {
            display: block;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="d-flex align-items-center mb-4">
                    <i class="bi bi-robot fs-2 text-white me-2"></i>
                    <h5 class="text-white mb-0">智能体配置</h5>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" onclick="showSection('overview')">
                        <i class="bi bi-house-door me-2"></i>概览
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('database')">
                        <i class="bi bi-database me-2"></i>数据库配置
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('auth')">
                        <i class="bi bi-shield-lock me-2"></i>身份认证
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('dify')">
                        <i class="bi bi-cpu me-2"></i>Dify对接
                    </a>
                    <a class="nav-link" href="#" onclick="showSection('files')">
                        <i class="bi bi-folder me-2"></i>文件管理
                    </a>
                </nav>
                
                <div class="mt-auto pt-4">
                    <div class="text-white-50 small">
                        <div><i class="bi bi-info-circle me-1"></i>版本 v1.1.1</div>
                        <div><i class="bi bi-gear me-1"></i>智能体系统</div>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <!-- 概览页面 -->
                <div id="overview" class="config-section active">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-house-door me-2"></i>系统概览</h2>
                        <button class="btn btn-outline-primary" onclick="generateConfig()">
                            <i class="bi bi-download me-1"></i>生成配置文件
                        </button>
                    </div>

                    <!-- 系统状态卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="status-indicator status-warning" id="db-status"></div>
                                    <h5 class="card-title">数据库</h5>
                                    <p class="card-text">
                                        <small class="text-warning" id="db-status-text">待配置</small>
                                    </p>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showSection('database')">配置</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="status-indicator status-warning" id="auth-status"></div>
                                    <h5 class="card-title">身份认证</h5>
                                    <p class="card-text">
                                        <small class="text-warning" id="auth-status-text">待配置</small>
                                    </p>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showSection('auth')">配置</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="status-indicator status-warning" id="dify-status"></div>
                                    <h5 class="card-title">Dify对接</h5>
                                    <p class="card-text">
                                        <small class="text-warning" id="dify-status-text">待配置</small>
                                    </p>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showSection('dify')">配置</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="status-indicator status-success"></div>
                                    <h5 class="card-title">文件管理</h5>
                                    <p class="card-text">
                                        <small class="text-success">正常</small>
                                    </p>
                                    <button class="btn btn-sm btn-outline-primary" onclick="showSection('files')">管理</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速配置向导 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>配置向导</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>🚀 快速开始</h6>
                                    <ol class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            1. 配置数据库连接
                                            <button class="btn btn-sm btn-primary" onclick="showSection('database')">去配置</button>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            2. 设置身份认证
                                            <button class="btn btn-sm btn-primary" onclick="showSection('auth')">去配置</button>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            3. 对接Dify平台
                                            <button class="btn btn-sm btn-primary" onclick="showSection('dify')">去配置</button>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            4. 生成配置文件
                                            <button class="btn btn-sm btn-success" onclick="generateConfig()">生成</button>
                                        </li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h6>📚 帮助文档</h6>
                                    <div class="list-group">
                                        <a href="/docs" class="list-group-item list-group-item-action">
                                            <i class="bi bi-book me-2"></i>API文档
                                        </a>
                                        <a href="/health" class="list-group-item list-group-item-action">
                                            <i class="bi bi-heart-pulse me-2"></i>系统状态
                                        </a>
                                        <a href="javascript:void(0)" onclick="showAlert('info', '智能体个性化学习系统 v1.1.1')" class="list-group-item list-group-item-action">
                                            <i class="bi bi-info-circle me-2"></i>关于系统
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据库配置页面 -->
                <div id="database" class="config-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-database me-2"></i>数据库配置</h2>
                        <button class="btn btn-outline-secondary" onclick="showSection('overview')">
                            <i class="bi bi-arrow-left me-1"></i>返回概览
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-gear me-2"></i>MySQL数据库配置</h5>
                                </div>
                                <div class="card-body">
                                    <form id="databaseForm">
                                        <div class="row">
                                            <div class="col-md-8 mb-3">
                                                <label for="db_host" class="form-label">数据库主机</label>
                                                <input type="text" class="form-control" id="db_host" value="localhost" required>
                                                <div class="form-text">数据库服务器地址</div>
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                <label for="db_port" class="form-label">端口</label>
                                                <input type="number" class="form-control" id="db_port" value="3306" required>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="db_user" class="form-label">用户名</label>
                                                <input type="text" class="form-control" id="db_user" value="root" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="db_password" class="form-label">密码</label>
                                                <input type="password" class="form-control" id="db_password">
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="db_name" class="form-label">数据库名称</label>
                                            <input type="text" class="form-control" id="db_name" value="intelligent_agent_db" required>
                                        </div>
                                        
                                        <button type="button" class="btn btn-primary" onclick="saveDatabase()">
                                            <i class="bi bi-check me-1"></i>保存配置
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>配置说明</h6>
                                </div>
                                <div class="card-body">
                                    <h6>📋 数据库要求</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check text-success me-1"></i>MySQL 8.0+</li>
                                        <li><i class="bi bi-check text-success me-1"></i>UTF8MB4字符集</li>
                                        <li><i class="bi bi-check text-success me-1"></i>InnoDB存储引擎</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 身份认证配置页面 -->
                <div id="auth" class="config-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-shield-lock me-2"></i>身份认证配置</h2>
                        <button class="btn btn-outline-secondary" onclick="showSection('overview')">
                            <i class="bi bi-arrow-left me-1"></i>返回概览
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-toggles me-2"></i>统一身份认证配置</h5>
                                </div>
                                <div class="card-body">
                                    <form id="authForm">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="sso_enabled">
                                                <label class="form-check-label" for="sso_enabled">
                                                    <strong>启用统一身份认证 (SSO)</strong>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="sso_type" class="form-label">认证类型</label>
                                            <select class="form-select" id="sso_type">
                                                <option value="oauth2">OAuth2</option>
                                                <option value="saml">SAML</option>
                                                <option value="ldap">LDAP</option>
                                            </select>
                                        </div>

                                        <div id="oauth2-config">
                                            <h6>OAuth2配置</h6>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="sso_client_id" class="form-label">Client ID</label>
                                                    <input type="text" class="form-control" id="sso_client_id">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="sso_client_secret" class="form-label">Client Secret</label>
                                                    <input type="password" class="form-control" id="sso_client_secret">
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="sso_discovery_url" class="form-label">Discovery URL</label>
                                                <input type="url" class="form-control" id="sso_discovery_url"
                                                       placeholder="https://your-provider.com/.well-known/openid_configuration">
                                            </div>
                                        </div>

                                        <button type="button" class="btn btn-primary" onclick="saveAuth()">
                                            <i class="bi bi-check me-1"></i>保存配置
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>配置说明</h6>
                                </div>
                                <div class="card-body">
                                    <h6>🔑 OAuth2配置</h6>
                                    <ul class="list-unstyled small">
                                        <li><i class="bi bi-check text-success me-1"></i>支持OpenID Connect</li>
                                        <li><i class="bi bi-check text-success me-1"></i>自动用户信息获取</li>
                                        <li><i class="bi bi-check text-success me-1"></i>安全令牌验证</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dify配置页面 -->
                <div id="dify" class="config-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-cpu me-2"></i>Dify对接配置</h2>
                        <button class="btn btn-outline-secondary" onclick="showSection('overview')">
                            <i class="bi bi-arrow-left me-1"></i>返回概览
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-cloud me-2"></i>Dify API配置</h5>
                                </div>
                                <div class="card-body">
                                    <form id="difyForm">
                                        <div class="mb-3">
                                            <label for="dify_api_url" class="form-label">Dify API地址</label>
                                            <input type="url" class="form-control" id="dify_api_url"
                                                   placeholder="https://api.dify.ai/v1">
                                        </div>

                                        <div class="mb-3">
                                            <label for="dify_api_key" class="form-label">API密钥</label>
                                            <input type="password" class="form-control" id="dify_api_key"
                                                   placeholder="app-xxxxxxxxxxxxxxxxxxxxx">
                                        </div>

                                        <div class="mb-3">
                                            <label for="dify_app_id" class="form-label">应用ID</label>
                                            <input type="text" class="form-control" id="dify_app_id"
                                                   placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                                        </div>

                                        <button type="button" class="btn btn-primary" onclick="saveDify()">
                                            <i class="bi bi-check me-1"></i>保存配置
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-list-check me-2"></i>配置步骤</h6>
                                </div>
                                <div class="card-body">
                                    <h6>🚀 Dify配置步骤</h6>
                                    <ol class="small">
                                        <li>在Dify中创建新应用</li>
                                        <li>获取应用API密钥和ID</li>
                                        <li>在此页面填写配置信息</li>
                                        <li>在Dify中添加HTTP工具</li>
                                        <li>配置智能体系统提示词</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件管理页面 -->
                <div id="files" class="config-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-folder me-2"></i>文件管理</h2>
                        <button class="btn btn-outline-secondary" onclick="showSection('overview')">
                            <i class="bi bi-arrow-left me-1"></i>返回概览
                        </button>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>文件管理说明</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="bi bi-lightbulb me-2"></i>
                                <strong>说明：</strong>文件管理功能需要启动完整的Web服务。请使用以下命令启动：
                            </div>

                            <div class="bg-light p-3 rounded">
                                <code>python3 config_ui.py</code>
                            </div>

                            <p class="mt-3">或者手动将文件放置到 <code>public/</code> 目录中。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 配置数据存储
        let config = {
            database: {},
            auth: {},
            dify: {}
        };

        // 显示指定的配置页面
        function showSection(sectionName, element) {
            // 隐藏所有页面
            document.querySelectorAll('.config-section').forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有导航链接的active状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // 显示指定页面
            document.getElementById(sectionName).classList.add('active');

            // 激活对应的导航链接
            if (element) {
                element.classList.add('active');
            }
        }

        // 保存数据库配置
        function saveDatabase() {
            config.database = {
                host: document.getElementById('db_host').value,
                port: document.getElementById('db_port').value,
                user: document.getElementById('db_user').value,
                password: document.getElementById('db_password').value,
                name: document.getElementById('db_name').value
            };

            updateStatus('db', 'success', '已配置');
            showAlert('success', '数据库配置保存成功！');
        }

        // 保存身份认证配置
        function saveAuth() {
            config.auth = {
                enabled: document.getElementById('sso_enabled').checked,
                type: document.getElementById('sso_type').value,
                client_id: document.getElementById('sso_client_id').value,
                client_secret: document.getElementById('sso_client_secret').value,
                discovery_url: document.getElementById('sso_discovery_url').value
            };

            updateStatus('auth', 'success', '已配置');
            showAlert('success', '身份认证配置保存成功！');
        }

        // 保存Dify配置
        function saveDify() {
            config.dify = {
                api_url: document.getElementById('dify_api_url').value,
                api_key: document.getElementById('dify_api_key').value,
                app_id: document.getElementById('dify_app_id').value
            };

            updateStatus('dify', 'success', '已配置');
            showAlert('success', 'Dify配置保存成功！');
        }

        // 更新状态指示器
        function updateStatus(type, status, text) {
            const indicator = document.getElementById(type + '-status');
            const statusText = document.getElementById(type + '-status-text');

            indicator.className = 'status-indicator status-' + status;
            statusText.textContent = text;
            statusText.className = 'text-' + status;
        }

        // 显示提示消息
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // 生成配置文件
        function generateConfig() {
            const envContent = generateEnvFile();
            const difyContent = generateDifyConfig();

            // 创建并下载.env文件
            downloadFile('.env', envContent);

            // 创建并下载dify_config.json文件
            downloadFile('dify_config.json', difyContent);

            showAlert('success', '配置文件已生成并下载！');
        }

        // 生成.env文件内容
        function generateEnvFile() {
            const db = config.database;
            const auth = config.auth;

            return `# 智能体个性化学习系统配置文件

# 数据库配置
DB_HOST=${db.host || 'localhost'}
DB_PORT=${db.port || '3306'}
DB_USER=${db.user || 'root'}
DB_PASSWORD=${db.password || ''}
DB_NAME=${db.name || 'intelligent_agent_db'}

# API配置
API_KEY=your-secret-api-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 统一身份认证配置
SSO_ENABLED=${auth.enabled ? 'true' : 'false'}
SSO_TYPE=${auth.type || 'oauth2'}
SSO_CLIENT_ID=${auth.client_id || ''}
SSO_CLIENT_SECRET=${auth.client_secret || ''}
SSO_DISCOVERY_URL=${auth.discovery_url || ''}
SSO_REDIRECT_URI=http://localhost:8000/auth/callback

# LDAP配置
LDAP_SERVER=
LDAP_PORT=389
LDAP_BASE_DN=
LDAP_USER_DN=
LDAP_PASSWORD=

# SAML配置
SAML_ENTITY_ID=
SAML_SSO_URL=
SAML_X509_CERT=
`;
        }

        // 生成Dify配置文件内容
        function generateDifyConfig() {
            const dify = config.dify;

            return JSON.stringify({
                api_url: dify.api_url || '',
                api_key: dify.api_key || '',
                app_id: dify.app_id || '',
                workflow_enabled: false
            }, null, 2);
        }

        // 下载文件
        function downloadFile(filename, content) {
            const element = document.createElement('a');
            element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
            element.setAttribute('download', filename);
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showAlert('info', '欢迎使用智能体系统配置界面！请按照左侧导航逐步配置各项参数。');
        });
    </script>
</body>
</html>

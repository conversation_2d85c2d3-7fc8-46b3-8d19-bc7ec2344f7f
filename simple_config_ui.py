#!/usr/bin/env python3
"""
智能体个性化学习系统 - 简化配置界面
不依赖数据库连接，仅用于配置文件管理
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
from fastapi import FastAPI, Request, Form, UploadFile, File, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

app = FastAPI(title="智能体系统配置界面", description="图形化配置统一身份认证、数据库、Dify对接")

# 创建必要的目录
os.makedirs("static", exist_ok=True)
os.makedirs("templates", exist_ok=True)
os.makedirs("public", exist_ok=True)
os.makedirs("uploads", exist_ok=True)

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/public", StaticFiles(directory="public"), name="public")
templates = Jinja2Templates(directory="templates")

# 配置文件路径
CONFIG_FILE = ".env"
DIFY_CONFIG_FILE = "dify_config.json"

class SimpleConfigManager:
    """简化配置管理器"""
    
    def __init__(self):
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        config = {}
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip().strip('"\'')
        return config
    
    def save_config(self, config: Dict[str, Any]):
        """保存配置"""
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            f.write("# 智能体个性化学习系统配置文件\n\n")
            
            # 数据库配置
            f.write("# 数据库配置\n")
            f.write(f"DB_HOST={config.get('DB_HOST', 'localhost')}\n")
            f.write(f"DB_PORT={config.get('DB_PORT', '3306')}\n")
            f.write(f"DB_USER={config.get('DB_USER', 'root')}\n")
            f.write(f"DB_PASSWORD={config.get('DB_PASSWORD', '')}\n")
            f.write(f"DB_NAME={config.get('DB_NAME', 'intelligent_agent_db')}\n\n")
            
            # API配置
            f.write("# API配置\n")
            f.write(f"API_KEY={config.get('API_KEY', 'your-secret-api-key-here')}\n")
            f.write(f"JWT_SECRET_KEY={config.get('JWT_SECRET_KEY', 'your-jwt-secret-key-here')}\n\n")
            
            # 统一身份认证配置
            f.write("# 统一身份认证配置\n")
            f.write(f"SSO_ENABLED={config.get('SSO_ENABLED', 'false')}\n")
            f.write(f"SSO_TYPE={config.get('SSO_TYPE', 'oauth2')}\n")
            f.write(f"SSO_CLIENT_ID={config.get('SSO_CLIENT_ID', '')}\n")
            f.write(f"SSO_CLIENT_SECRET={config.get('SSO_CLIENT_SECRET', '')}\n")
            f.write(f"SSO_DISCOVERY_URL={config.get('SSO_DISCOVERY_URL', '')}\n")
            f.write(f"SSO_REDIRECT_URI={config.get('SSO_REDIRECT_URI', '')}\n\n")
            
            # LDAP配置
            f.write("# LDAP配置\n")
            f.write(f"LDAP_SERVER={config.get('LDAP_SERVER', '')}\n")
            f.write(f"LDAP_PORT={config.get('LDAP_PORT', '389')}\n")
            f.write(f"LDAP_BASE_DN={config.get('LDAP_BASE_DN', '')}\n")
            f.write(f"LDAP_USER_DN={config.get('LDAP_USER_DN', '')}\n")
            f.write(f"LDAP_PASSWORD={config.get('LDAP_PASSWORD', '')}\n\n")
            
            # SAML配置
            f.write("# SAML配置\n")
            f.write(f"SAML_ENTITY_ID={config.get('SAML_ENTITY_ID', '')}\n")
            f.write(f"SAML_SSO_URL={config.get('SAML_SSO_URL', '')}\n")
            f.write(f"SAML_X509_CERT={config.get('SAML_X509_CERT', '')}\n\n")
        
        self.config = config

config_manager = SimpleConfigManager()

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {
        "request": request,
        "config": config_manager.config
    })

@app.get("/database", response_class=HTMLResponse)
async def database_config(request: Request):
    """数据库配置页面"""
    return templates.TemplateResponse("database.html", {
        "request": request,
        "config": config_manager.config
    })

@app.post("/database")
async def save_database_config(
    request: Request,
    db_host: str = Form(...),
    db_port: str = Form(...),
    db_user: str = Form(...),
    db_password: str = Form(...),
    db_name: str = Form(...)
):
    """保存数据库配置"""
    config = config_manager.config.copy()
    config.update({
        'DB_HOST': db_host,
        'DB_PORT': db_port,
        'DB_USER': db_user,
        'DB_PASSWORD': db_password,
        'DB_NAME': db_name
    })
    
    config_manager.save_config(config)
    return templates.TemplateResponse("database.html", {
        "request": request,
        "config": config,
        "success": "数据库配置保存成功！"
    })

@app.get("/auth", response_class=HTMLResponse)
async def auth_config(request: Request):
    """身份认证配置页面"""
    return templates.TemplateResponse("auth.html", {
        "request": request,
        "config": config_manager.config
    })

@app.post("/auth")
async def save_auth_config(
    request: Request,
    sso_enabled: str = Form("false"),
    sso_type: str = Form("oauth2"),
    sso_client_id: str = Form(""),
    sso_client_secret: str = Form(""),
    sso_discovery_url: str = Form(""),
    sso_redirect_uri: str = Form(""),
    ldap_server: str = Form(""),
    ldap_port: str = Form("389"),
    ldap_base_dn: str = Form(""),
    ldap_user_dn: str = Form(""),
    ldap_password: str = Form(""),
    saml_entity_id: str = Form(""),
    saml_sso_url: str = Form(""),
    saml_x509_cert: str = Form("")
):
    """保存身份认证配置"""
    config = config_manager.config.copy()
    config.update({
        'SSO_ENABLED': sso_enabled,
        'SSO_TYPE': sso_type,
        'SSO_CLIENT_ID': sso_client_id,
        'SSO_CLIENT_SECRET': sso_client_secret,
        'SSO_DISCOVERY_URL': sso_discovery_url,
        'SSO_REDIRECT_URI': sso_redirect_uri,
        'LDAP_SERVER': ldap_server,
        'LDAP_PORT': ldap_port,
        'LDAP_BASE_DN': ldap_base_dn,
        'LDAP_USER_DN': ldap_user_dn,
        'LDAP_PASSWORD': ldap_password,
        'SAML_ENTITY_ID': saml_entity_id,
        'SAML_SSO_URL': saml_sso_url,
        'SAML_X509_CERT': saml_x509_cert
    })
    
    config_manager.save_config(config)
    return templates.TemplateResponse("auth.html", {
        "request": request,
        "config": config,
        "success": "身份认证配置保存成功！"
    })

@app.get("/dify", response_class=HTMLResponse)
async def dify_config(request: Request):
    """Dify配置页面"""
    dify_config = {}
    if os.path.exists(DIFY_CONFIG_FILE):
        with open(DIFY_CONFIG_FILE, 'r', encoding='utf-8') as f:
            dify_config = json.load(f)
    
    return templates.TemplateResponse("dify.html", {
        "request": request,
        "config": config_manager.config,
        "dify_config": dify_config
    })

@app.post("/dify")
async def save_dify_config(
    request: Request,
    dify_api_url: str = Form(""),
    dify_api_key: str = Form(""),
    dify_app_id: str = Form(""),
    workflow_enabled: str = Form("false")
):
    """保存Dify配置"""
    dify_config = {
        'api_url': dify_api_url,
        'api_key': dify_api_key,
        'app_id': dify_app_id,
        'workflow_enabled': workflow_enabled == "true"
    }
    
    with open(DIFY_CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(dify_config, f, indent=2, ensure_ascii=False)
    
    return templates.TemplateResponse("dify.html", {
        "request": request,
        "config": config_manager.config,
        "dify_config": dify_config,
        "success": "Dify配置保存成功！"
    })

@app.get("/files", response_class=HTMLResponse)
async def file_manager(request: Request):
    """文件管理页面"""
    public_files = []
    if os.path.exists("public"):
        for file in os.listdir("public"):
            file_path = os.path.join("public", file)
            if os.path.isfile(file_path):
                stat = os.stat(file_path)
                public_files.append({
                    'name': file,
                    'size': stat.st_size,
                    'modified': stat.st_mtime
                })
    
    return templates.TemplateResponse("files.html", {
        "request": request,
        "public_files": public_files
    })

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """上传文件到public目录"""
    try:
        file_path = os.path.join("public", file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        return JSONResponse({"success": True, "message": f"文件 {file.filename} 上传成功"})
    except Exception as e:
        return JSONResponse({"success": False, "message": f"上传失败: {str(e)}"})

@app.delete("/files/{filename}")
async def delete_file(filename: str):
    """删除文件"""
    try:
        file_path = os.path.join("public", filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return JSONResponse({"success": True, "message": f"文件 {filename} 删除成功"})
        else:
            return JSONResponse({"success": False, "message": "文件不存在"})
    except Exception as e:
        return JSONResponse({"success": False, "message": f"删除失败: {str(e)}"})

@app.post("/test-db")
async def test_database():
    """测试数据库连接（简化版）"""
    return JSONResponse({"success": True, "message": "配置已保存，请安装mysql-connector-python后测试连接"})

if __name__ == "__main__":
    print("🚀 启动智能体系统配置界面...")
    print("📱 访问地址: http://localhost:8080")
    print("🔧 配置完成后请重启主服务")
    uvicorn.run(app, host="0.0.0.0", port=8080)

#!/bin/bash

# 智能体个性化学习系统快速启动脚本
# Quick Start Script for Intelligent Agent System

set -e

echo "🚀 智能体个性化学习系统快速启动"
echo "=================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    echo "   访问: https://docs.docker.com/get-docker/"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    echo "   访问: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 创建环境配置文件
if [ ! -f .env ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件配置您的数据库密码和API密钥"
    echo "   默认API密钥: dify-agent-secret-key-2024"
fi

# 创建日志目录
mkdir -p logs

echo "🐳 启动Docker服务..."

# 构建并启动服务
docker-compose up -d --build

echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ API服务启动成功"
    echo "📖 API文档: http://localhost:8000/docs"
    echo "🔍 健康检查: http://localhost:8000/health"
else
    echo "❌ API服务启动失败，请检查日志:"
    echo "   docker-compose logs intelligent-agent-api"
    exit 1
fi

# 检查数据库
echo "🔍 检查数据库连接..."
if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败，请检查配置"
    exit 1
fi

echo ""
echo "🎉 系统启动完成！"
echo "=================================="
echo "📊 服务状态:"
echo "   - API服务: http://localhost:8000"
echo "   - API文档: http://localhost:8000/docs"
echo "   - 数据库管理: http://localhost:8080 (phpMyAdmin)"
echo ""
echo "🧪 测试系统:"
echo "   python test_system.py"
echo ""
echo "📚 更多信息:"
echo "   - 部署指南: deployment_guide.md"
echo "   - Dify配置: dify_configuration_guide.md"
echo "   - 项目文档: README.md"
echo ""
echo "🛑 停止服务:"
echo "   docker-compose down"

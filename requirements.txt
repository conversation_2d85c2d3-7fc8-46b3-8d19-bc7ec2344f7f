# 智能体个性化学习系统依赖包

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库连接
mysql-connector-python==8.2.0

# 安全和认证
python-multipart==0.0.6
PyJWT==2.8.0
requests==2.31.0

# 日志和监控
python-json-logger==2.0.7

# 开发和测试工具
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 环境配置
python-dotenv==1.0.0

# 数据处理
pandas==2.1.3
numpy==1.25.2

# 时间处理
python-dateutil==2.8.2

# Web界面依赖
jinja2==3.1.2
aiofiles==23.2.1

#!/usr/bin/env python3
"""
测试配置界面的简单HTTP服务器
不依赖FastAPI，使用Python内置的http.server
"""

import os
import json
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from datetime import datetime

class ConfigHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/config':
            self.serve_config_page()
        elif self.path == '/health':
            self.serve_health()
        elif self.path.startswith('/static/') or self.path.startswith('/public/'):
            self.serve_static_file()
        else:
            self.send_error(404, "Page not found")
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/save_config':
            self.save_config()
        else:
            self.send_error(404, "API not found")
    
    def serve_config_page(self):
        """提供配置界面"""
        try:
            with open('config.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Configuration page not found")
    
    def serve_health(self):
        """健康检查"""
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "config-interface",
            "version": "1.0.0"
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(health_data).encode('utf-8'))
    
    def serve_static_file(self):
        """提供静态文件"""
        file_path = self.path[1:]  # 移除开头的 '/'
        
        if os.path.exists(file_path) and os.path.isfile(file_path):
            try:
                with open(file_path, 'rb') as f:
                    content = f.read()
                
                # 根据文件扩展名设置Content-Type
                if file_path.endswith('.html'):
                    content_type = 'text/html'
                elif file_path.endswith('.css'):
                    content_type = 'text/css'
                elif file_path.endswith('.js'):
                    content_type = 'application/javascript'
                elif file_path.endswith('.json'):
                    content_type = 'application/json'
                else:
                    content_type = 'application/octet-stream'
                
                self.send_response(200)
                self.send_header('Content-type', content_type)
                self.end_headers()
                self.wfile.write(content)
            except Exception as e:
                self.send_error(500, f"Error reading file: {str(e)}")
        else:
            self.send_error(404, "File not found")
    
    def save_config(self):
        """保存配置"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            # 解析POST数据
            data = urllib.parse.parse_qs(post_data.decode('utf-8'))
            
            # 这里可以处理配置保存逻辑
            # 目前只是返回成功响应
            
            response = {
                "status": "success",
                "message": "Configuration saved successfully",
                "timestamp": datetime.now().isoformat()
            }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
            
        except Exception as e:
            error_response = {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }
            
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def main():
    """启动服务器"""
    port = 8080
    server_address = ('', port)
    
    # 创建必要的目录
    os.makedirs('static', exist_ok=True)
    os.makedirs('public', exist_ok=True)
    
    httpd = HTTPServer(server_address, ConfigHandler)
    
    print(f"🚀 配置界面服务器启动成功!")
    print(f"📱 访问地址: http://localhost:{port}")
    print(f"🔧 配置界面: http://localhost:{port}/config")
    print(f"🔍 健康检查: http://localhost:{port}/health")
    print(f"📁 当前目录: {os.getcwd()}")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    main()

# 📚 学生学情分析工具 v1.2.0 部署指南

## 🎯 系统概述

学生学情分析工具是一个基于Dify智能体的个性化学习分析系统，提供：

- **学生界面**: 身份认证 → 智能体对话分析
- **管理后台**: 系统配置、API管理、健康监控

## 🔐 安全特性

### ✅ v1.2.0 安全改进
- 学生界面完全隔离，无法访问管理功能
- 管理后台需要身份认证保护
- 管理员账号: `admin` / `NacooLab@2024`
- 安全的cookie认证机制

## 📦 快速部署

### 方法一：使用Docker镜像文件

```bash
# 1. 加载镜像
docker load -i student-analysis-tool-v1.2.0.tar

# 2. 启动容器
docker run -d \
  --name student-analysis-tool \
  -p 8000:8000 \
  intelligent-agent-system:v1.2.0

# 3. 访问系统
# 学生界面: http://localhost:8000
# 管理后台: http://localhost:8000/admin
```

### 方法二：从源码构建

```bash
# 1. 克隆代码
git clone https://github.com/nacoo/smartstudio.git
cd smartstudio

# 2. 构建镜像
docker build -t intelligent-agent-system:v1.2.0 .

# 3. 启动容器
docker run -d \
  --name student-analysis-tool \
  -p 8000:8000 \
  intelligent-agent-system:v1.2.0
```

## 🚀 系统配置

### 1. 访问管理后台
1. 打开 http://localhost:8000/admin
2. 使用管理员账号登录: `admin` / `NacooLab@2024`
3. 进入配置界面

### 2. 配置数据库连接
- 数据库类型: MySQL/PostgreSQL
- 连接信息: 主机、端口、数据库名、用户名、密码
- 测试连接确保配置正确

### 3. 配置Dify对接
- Dify API地址: https://api.dify.ai/v1
- API密钥: 从Dify应用获取
- 应用ID: Dify应用唯一标识

### 4. 配置身份认证
- 支持OIDC、SAML等统一身份认证
- 配置客户端ID和密钥
- 设置发现URL

## 🎓 学生使用流程

1. **访问主页**: http://localhost:8000
2. **点击"开始使用"**: 进入登录页面
3. **学生登录**: 使用演示账号 `demo_user` / `demo_pass`
4. **智能体对话**: 获得个性化学习分析和建议

## 🔧 管理员功能

### 访问管理后台
- URL: http://localhost:8000/admin
- 账号: `admin`
- 密码: `NacooLab@2024`

### 管理功能
- **配置界面**: 系统参数配置
- **API文档**: 接口文档查看
- **健康检查**: 系统状态监控
- **测试API**: 用户画像API测试
- **安全登出**: 清除认证状态

## 📊 API接口

### 用户画像查询
```bash
curl -H "X-API-Key: dify-agent-secret-key-2024" \
     "http://localhost:8000/get_user_profile?user_id=user_001"
```

### 健康检查
```bash
curl "http://localhost:8000/health"
```

### 批量查询
```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -H "X-API-Key: dify-agent-secret-key-2024" \
     -d '{"user_ids": ["user_001", "user_002"]}' \
     "http://localhost:8000/batch_get_user_profiles"
```

## 🔗 Dify智能体配置

### 1. 创建HTTP工具
- 工具名称: `获取用户画像`
- 请求方法: GET
- URL: `http://your-server:8000/get_user_profile`
- 请求头: `X-API-Key: dify-agent-secret-key-2024`

### 2. 配置参数
- 输入参数: `user_id` (string, 必填)
- 输出参数: `user_profile_description` (string)

### 3. 智能体提示词
```
# 角色定义
你是专业的学生学情分析助手。

# 用户画像信息
{{#user_profile.user_profile_description#}}

# 任务
基于用户画像提供个性化的学习分析和建议。
```

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   docker logs student-analysis-tool
   ```

2. **数据库连接失败**
   - 检查数据库配置
   - 确认网络连通性
   - 验证用户权限

3. **Dify对接失败**
   - 验证API密钥
   - 检查网络连接
   - 确认应用ID正确

4. **管理员无法登录**
   - 确认账号: `admin`
   - 确认密码: `NacooLab@2024`
   - 清除浏览器cookie

### 日志查看
```bash
# 查看容器日志
docker logs -f student-analysis-tool

# 进入容器调试
docker exec -it student-analysis-tool bash
```

## 📝 版本信息

- **版本**: v1.2.0
- **发布日期**: 2024-07-10
- **开发团队**: NacooLab
- **技术支持**: 智能体个性化学习系统

## 🔄 升级说明

从v1.1.x升级到v1.2.0:

1. **停止旧容器**
   ```bash
   docker stop student-analysis-tool
   docker rm student-analysis-tool
   ```

2. **加载新镜像**
   ```bash
   docker load -i student-analysis-tool-v1.2.0.tar
   ```

3. **启动新容器**
   ```bash
   docker run -d \
     --name student-analysis-tool \
     -p 8000:8000 \
     intelligent-agent-system:v1.2.0
   ```

4. **重新配置系统**
   - 访问管理后台重新配置
   - 测试学生界面功能
   - 验证Dify对接正常

## 📞 技术支持

如有问题，请联系技术支持团队或查看项目文档。

---
**© 2024 NacooLab - 学生学情分析工具**

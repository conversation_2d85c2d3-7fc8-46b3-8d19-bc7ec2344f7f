# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
.venv/
ENV/
env/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
*.md
docs/

# Test files
test_*.py
*_test.py
tests/
test_report.json

# Temporary files
tmp/
temp/
*.tmp

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/

# Logs
logs/
*.log

# Database files
*.db
*.sqlite
*.sqlite3
backup_*.sql*

# Tar files
*.tar
*.tar.gz

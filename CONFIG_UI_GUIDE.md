# 🎨 智能体系统图形化配置界面使用指南

## 📋 概述

智能体个性化学习系统提供了一个直观的图形化配置界面，让您可以轻松配置统一身份认证、数据库连接、Dify对接等各项参数，无需手动编辑配置文件。

## 🚀 快速开始

### 方法一：使用静态HTML界面（推荐）

1. **打开配置界面**：
   ```bash
   ./open-config.sh
   ```
   或直接在浏览器中打开 `config.html` 文件

2. **逐步配置各项参数**：
   - 数据库配置
   - 身份认证设置
   - Dify对接配置

3. **生成配置文件**：
   点击"生成配置文件"按钮，自动下载 `.env` 和 `dify_config.json` 文件

4. **部署配置文件**：
   将下载的配置文件放置到项目根目录

### 方法二：使用Web服务界面

1. **安装依赖**：
   ```bash
   pip3 install -r requirements.txt
   ```

2. **启动配置服务**：
   ```bash
   python3 config_ui.py
   ```

3. **访问配置界面**：
   打开浏览器访问 http://localhost:8080

## 🔧 配置项说明

### 1. 数据库配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| 数据库主机 | MySQL服务器地址 | localhost | ************* |
| 端口 | MySQL服务端口 | 3306 | 3306 |
| 用户名 | 数据库用户名 | root | myuser |
| 密码 | 数据库密码 | (空) | mypassword |
| 数据库名称 | 系统使用的数据库名 | intelligent_agent_db | my_db |

**要求**：
- MySQL 8.0+
- UTF8MB4字符集
- InnoDB存储引擎

### 2. 身份认证配置

#### OAuth2配置
| 配置项 | 说明 | 示例 |
|--------|------|------|
| Client ID | OAuth2应用客户端ID | abc123def456 |
| Client Secret | OAuth2应用客户端密钥 | secret_key_here |
| Discovery URL | OpenID Connect发现端点 | https://auth.example.com/.well-known/openid_configuration |

#### 支持的认证类型
- **OAuth2**: 支持OpenID Connect标准
- **SAML**: SAML 2.0协议
- **LDAP**: Active Directory支持

### 3. Dify对接配置

| 配置项 | 说明 | 示例 |
|--------|------|------|
| API地址 | Dify平台API基础地址 | https://api.dify.ai/v1 |
| API密钥 | Dify应用API密钥 | app-xxxxxxxxxxxxxxxxxxxxx |
| 应用ID | Dify应用唯一标识符 | xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx |

## 📁 文件管理

配置界面支持文件上传到 `public/` 目录，用于存放Dify前端需要的静态文件：

- HTML页面文件
- CSS样式文件
- JavaScript脚本
- 图片和媒体文件

**注意**：文件管理功能需要启动完整的Web服务（`python3 config_ui.py`）。

## 🎯 配置流程

### 步骤1：数据库配置
1. 填写MySQL数据库连接信息
2. 点击"保存配置"
3. 系统会验证连接（Web服务模式）

### 步骤2：身份认证设置
1. 选择是否启用SSO
2. 选择认证类型（OAuth2/SAML/LDAP）
3. 填写相应的认证参数
4. 点击"保存配置"

### 步骤3：Dify对接配置
1. 填写Dify API地址和密钥
2. 填写应用ID
3. 点击"保存配置"

### 步骤4：生成配置文件
1. 点击"生成配置文件"按钮
2. 自动下载 `.env` 和 `dify_config.json` 文件
3. 将文件放置到项目根目录

### 步骤5：启动系统
```bash
python3 main.py
```

## 🔍 生成的配置文件

### .env文件示例
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=mypassword
DB_NAME=intelligent_agent_db

# API配置
API_KEY=your-secret-api-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 统一身份认证配置
SSO_ENABLED=true
SSO_TYPE=oauth2
SSO_CLIENT_ID=abc123def456
SSO_CLIENT_SECRET=secret_key_here
SSO_DISCOVERY_URL=https://auth.example.com/.well-known/openid_configuration
SSO_REDIRECT_URI=http://localhost:8000/auth/callback
```

### dify_config.json文件示例
```json
{
  "api_url": "https://api.dify.ai/v1",
  "api_key": "app-xxxxxxxxxxxxxxxxxxxxx",
  "app_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "workflow_enabled": false
}
```

## 🛠️ 故障排除

### 常见问题

1. **配置界面无法打开**
   - 确保 `config.html` 文件存在
   - 尝试直接在浏览器中打开文件

2. **Web服务启动失败**
   - 检查Python依赖是否安装完整
   - 确保端口8080未被占用

3. **配置文件下载失败**
   - 检查浏览器是否阻止了文件下载
   - 尝试手动复制配置内容

4. **数据库连接测试失败**
   - 检查MySQL服务是否运行
   - 验证用户名密码是否正确
   - 确保数据库已创建

## 📚 相关文档

- [部署指南](deployment_guide.md)
- [Dify配置指南](dify_configuration_guide.md)
- [API文档](http://localhost:8000/docs)
- [GitHub仓库](https://github.com/nacoo/smartstudio)

## 🎉 完成配置

配置完成后，您的智能体系统将具备：

- ✅ 统一身份认证集成
- ✅ 多维度用户画像查询
- ✅ Dify智能体无缝对接
- ✅ 个性化学习服务

系统将自动获取用户身份信息，查询数据库生成精确的用户画像，并在Dify中隐藏地使用这些信息提供个性化的学习指导服务。

#!/usr/bin/env python3
"""
智能体个性化学习系统测试脚本
用于验证系统各个组件的功能
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# 测试配置
API_BASE_URL = "http://localhost:8000"
API_KEY = "dify-agent-secret-key-2024"
TEST_USER_IDS = ["user_001", "user_002", "user_003"]

class SystemTester:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
        self.test_results = []
    
    def run_test(self, test_name: str, test_func):
        """运行单个测试"""
        print(f"\n🧪 运行测试: {test_name}")
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            if result:
                print(f"✅ {test_name} - 通过 ({end_time - start_time:.2f}s)")
                self.test_results.append({"test": test_name, "status": "PASS", "time": end_time - start_time})
                return True
            else:
                print(f"❌ {test_name} - 失败")
                self.test_results.append({"test": test_name, "status": "FAIL", "time": end_time - start_time})
                return False
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
            self.test_results.append({"test": test_name, "status": "ERROR", "error": str(e)})
            return False
    
    def test_api_health(self) -> bool:
        """测试API健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"   API状态: {data.get('status')}")
                print(f"   数据库状态: {data.get('database')}")
                return data.get('status') == 'healthy'
            return False
        except Exception as e:
            print(f"   健康检查失败: {e}")
            return False
    
    def test_root_endpoint(self) -> bool:
        """测试根端点"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"   API版本: {data.get('version')}")
                print(f"   服务状态: {data.get('status')}")
                return True
            return False
        except Exception as e:
            print(f"   根端点测试失败: {e}")
            return False
    
    def test_user_profile_query(self) -> bool:
        """测试用户画像查询"""
        success_count = 0
        
        for user_id in TEST_USER_IDS:
            try:
                response = requests.get(
                    f"{self.base_url}/get_user_profile",
                    params={"user_id": user_id},
                    headers=self.headers,
                    timeout=15
                )
                
                if response.status_code == 200:
                    data = response.json()
                    profile_desc = data.get('user_profile_description', '')
                    
                    if profile_desc and len(profile_desc) > 50:
                        print(f"   ✅ 用户 {user_id}: 画像长度 {len(profile_desc)} 字符")
                        success_count += 1
                    else:
                        print(f"   ❌ 用户 {user_id}: 画像内容不足")
                else:
                    print(f"   ❌ 用户 {user_id}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ 用户 {user_id}: 异常 {e}")
        
        return success_count == len(TEST_USER_IDS)
    
    def test_user_profile_detail(self) -> bool:
        """测试用户画像详细信息"""
        try:
            user_id = TEST_USER_IDS[0]  # 使用第一个测试用户
            response = requests.get(
                f"{self.base_url}/get_user_profile",
                params={"user_id": user_id, "include_raw_data": "true"},
                headers=self.headers,
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                raw_data = data.get('raw_data', {})
                
                # 检查各个数据维度
                checks = {
                    "基本信息": bool(raw_data.get('basic_info')),
                    "学生信息": bool(raw_data.get('student_info')),
                    "学习评估": bool(raw_data.get('assessments')),
                    "学习记录": bool(raw_data.get('learning_records')),
                    "兴趣偏好": bool(raw_data.get('interests')),
                    "学习偏好": bool(raw_data.get('preferences')),
                    "个性特征": bool(raw_data.get('personality'))
                }
                
                for check_name, check_result in checks.items():
                    status = "✅" if check_result else "❌"
                    print(f"   {status} {check_name}: {'有数据' if check_result else '无数据'}")
                
                return all(checks.values())
            
            return False
            
        except Exception as e:
            print(f"   详细信息测试失败: {e}")
            return False
    
    def test_batch_query(self) -> bool:
        """测试批量查询"""
        try:
            response = requests.post(
                f"{self.base_url}/batch_user_profiles",
                json=TEST_USER_IDS,
                headers=self.headers,
                timeout=20
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                success_count = data.get('success_count', 0)
                total_count = data.get('total_count', 0)
                
                print(f"   批量查询结果: {success_count}/{total_count} 成功")
                
                for result in results:
                    user_id = result.get('user_id')
                    status = result.get('status')
                    status_icon = "✅" if status == "success" else "❌"
                    print(f"   {status_icon} {user_id}: {status}")
                
                return success_count == total_count
            
            return False
            
        except Exception as e:
            print(f"   批量查询测试失败: {e}")
            return False
    
    def test_invalid_user(self) -> bool:
        """测试无效用户ID"""
        try:
            response = requests.get(
                f"{self.base_url}/get_user_profile",
                params={"user_id": "invalid_user_999"},
                headers=self.headers,
                timeout=10
            )
            
            # 应该返回404错误
            if response.status_code == 404:
                print("   ✅ 正确处理无效用户ID")
                return True
            else:
                print(f"   ❌ 未正确处理无效用户ID，返回状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   无效用户测试失败: {e}")
            return False
    
    def test_api_key_validation(self) -> bool:
        """测试API密钥验证"""
        try:
            # 测试无效API密钥
            invalid_headers = {
                "X-API-Key": "invalid-key",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.base_url}/get_user_profile",
                params={"user_id": TEST_USER_IDS[0]},
                headers=invalid_headers,
                timeout=10
            )
            
            # 应该返回403错误
            if response.status_code == 403:
                print("   ✅ 正确验证API密钥")
                return True
            else:
                print(f"   ❌ API密钥验证失败，返回状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   API密钥验证测试失败: {e}")
            return False
    
    def test_response_format(self) -> bool:
        """测试响应格式"""
        try:
            user_id = TEST_USER_IDS[0]
            response = requests.get(
                f"{self.base_url}/get_user_profile",
                params={"user_id": user_id},
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查必需字段
                required_fields = ['user_id', 'user_profile_description', 'timestamp', 'status']
                missing_fields = [field for field in required_fields if field not in data]
                
                if not missing_fields:
                    print("   ✅ 响应格式正确")
                    
                    # 检查数据类型
                    if (isinstance(data['user_id'], str) and 
                        isinstance(data['user_profile_description'], str) and
                        isinstance(data['timestamp'], str) and
                        data['status'] == 'success'):
                        print("   ✅ 数据类型正确")
                        return True
                    else:
                        print("   ❌ 数据类型不正确")
                        return False
                else:
                    print(f"   ❌ 缺少必需字段: {missing_fields}")
                    return False
            
            return False
            
        except Exception as e:
            print(f"   响应格式测试失败: {e}")
            return False
    
    def test_performance(self) -> bool:
        """测试性能"""
        try:
            user_id = TEST_USER_IDS[0]
            response_times = []
            
            # 执行10次查询测试响应时间
            for i in range(10):
                start_time = time.time()
                response = requests.get(
                    f"{self.base_url}/get_user_profile",
                    params={"user_id": user_id},
                    headers=self.headers,
                    timeout=10
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    response_times.append(end_time - start_time)
                else:
                    print(f"   ❌ 第{i+1}次请求失败")
                    return False
            
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            print(f"   平均响应时间: {avg_time:.3f}s")
            print(f"   最大响应时间: {max_time:.3f}s")
            print(f"   最小响应时间: {min_time:.3f}s")
            
            # 性能标准：平均响应时间小于2秒
            if avg_time < 2.0:
                print("   ✅ 性能测试通过")
                return True
            else:
                print("   ❌ 性能测试失败，响应时间过长")
                return False
                
        except Exception as e:
            print(f"   性能测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始智能体个性化学习系统测试")
        print("=" * 50)
        
        tests = [
            ("API健康检查", self.test_api_health),
            ("根端点测试", self.test_root_endpoint),
            ("用户画像查询", self.test_user_profile_query),
            ("用户画像详细信息", self.test_user_profile_detail),
            ("批量查询测试", self.test_batch_query),
            ("无效用户处理", self.test_invalid_user),
            ("API密钥验证", self.test_api_key_validation),
            ("响应格式验证", self.test_response_format),
            ("性能测试", self.test_performance)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            if self.run_test(test_name, test_func):
                passed += 1
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果汇总: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统运行正常。")
            return True
        else:
            print("⚠️  部分测试失败，请检查系统配置。")
            return False
    
    def generate_report(self):
        """生成测试报告"""
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_tests": len(self.test_results),
            "passed": len([r for r in self.test_results if r["status"] == "PASS"]),
            "failed": len([r for r in self.test_results if r["status"] == "FAIL"]),
            "errors": len([r for r in self.test_results if r["status"] == "ERROR"]),
            "results": self.test_results
        }
        
        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 测试报告已保存到: test_report.json")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        api_url = sys.argv[1]
    else:
        api_url = API_BASE_URL
    
    if len(sys.argv) > 2:
        api_key = sys.argv[2]
    else:
        api_key = API_KEY
    
    print(f"测试目标: {api_url}")
    print(f"API密钥: {api_key[:10]}...")
    
    tester = SystemTester(api_url, api_key)
    success = tester.run_all_tests()
    tester.generate_report()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
